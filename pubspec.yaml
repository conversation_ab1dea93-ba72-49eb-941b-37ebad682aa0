name: niseamen_flutter
description: "NiseAmen - Digital hymn book for Église du christianisme céleste"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # State Management
  flutter_riverpod: ^2.6.1

  # Local Storage
  shared_preferences: ^2.3.3

  # UI Components
  smooth_page_indicator: ^1.2.0
  google_fonts: ^6.2.1

  # Routing
  go_router: ^14.6.2

  # Firebase
  firebase_core: ^3.8.0
  firebase_auth: ^5.3.3
  cloud_firestore: ^5.6.0
  firebase_storage: ^12.3.4
  firebase_analytics: ^11.3.4

  # Social Authentication
  google_sign_in: ^6.2.2
  sign_in_with_apple: ^6.1.4

  # Utilities
  crypto: ^3.0.6

  # HTTP Client
  http: ^1.2.2
  dio: ^5.7.0

  # JSON Serialization
  json_annotation: ^4.9.0

  # Audio Playback
  just_audio: ^0.9.42
  audio_service: ^0.18.15
  audio_session: ^0.1.21

  # UI Components
  cached_network_image: ^3.4.1

  # Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Utilities
  timezone: ^0.9.4
  intl: ^0.19.0

  # Permission handling
  permission_handler: ^11.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # Splash Screen Generator
  flutter_native_splash: ^2.4.1

  # JSON Serialization
  json_serializable: ^6.8.0
  build_runner: ^2.4.13

  # Storage Code Generation
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Native Splash Configuration
flutter_native_splash:
  color: "#ffffff"
  image: assets/images/splash_logo.png
  color_dark: "#000000"
  image_dark: assets/images/splash_logo.png
  android_12:
    image: assets/images/splash_logo.png
    color: "#ffffff"
    image_dark: assets/images/splash_logo.png
    color_dark: "#000000"
