{"cli": {"version": ">=3.0.0", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal"}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "env": {"APP_VARIANT": "preview"}, "channel": "preview", "developmentClient": false, "extends": "production"}, "production": {"autoIncrement": true, "android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}, "channel": "production", "developmentClient": false}}, "submit": {"production": {}}}