import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Song } from '@/types/song';
import { fetchSongs } from '@/services/songs';
import { playSong, togglePlayback, seekTo, unloadSound, initAudio } from '@/services/audio';

// Cache expiration time in milliseconds (24 hours)
const SONGS_CACHE_KEY = 'songs_cache';
const CACHE_EXPIRATION = 24 * 60 * 60 * 1000;

// Separate minimal state for playback progress
interface PlaybackProgress {
  currentTime: number;
  duration: number;
}

let playbackProgress: PlaybackProgress = {
  currentTime: 0,
  duration: 0
};

interface PlayerState {
  currentSong: Song | null;
  isPlaying: boolean;
  queue: Song[];
  selectedLanguage: string;
  isLoading: boolean;
  error: string | null;
  lastFetchTime: number | null;
  isLyricsFullScreen: boolean;
  preventNavigation: boolean;
  
  // Actions
  play: () => Promise<void>;
  pause: () => Promise<void>;
  playPause: () => Promise<void>;
  setCurrentSong: (song: Song) => Promise<void>;
  seekToTime: (time: number) => Promise<void>;
  nextSong: () => Promise<void>;
  previousSong: () => Promise<void>;
  setSelectedLanguage: (language: string) => void;
  loadSongs: (forceRefresh?: boolean) => Promise<void>;
  setError: (error: string | null) => void;
  cleanup: () => Promise<void>;
  setLyricsFullScreen: (isFullScreen: boolean) => void;
  getProgress: () => PlaybackProgress;
  updateProgress: (time: number, duration: number) => void;
}

// Initial state to guarantee queue is an array
const initialState: PlayerState = {
  preventNavigation: false,
  currentSong: null,
  isPlaying: false,
  queue: [],
  selectedLanguage: 'goun',
  isLoading: false,
  error: null,
  lastFetchTime: null,
  isLyricsFullScreen: false,
  
  // These will be overwritten by the actual implementations
  play: async () => {},
  pause: async () => {},
  playPause: async () => {},
  setCurrentSong: async () => {},
  seekToTime: async () => {},
  nextSong: async () => {},
  previousSong: async () => {},
  setSelectedLanguage: () => {},
  loadSongs: async () => {},
  setError: () => {},
  cleanup: async () => {},
  setLyricsFullScreen: () => {},
  getProgress: () => ({ currentTime: 0, duration: 0 }),
  updateProgress: () => {},
};

export const usePlayerStore = create<PlayerState>()(
  persist(
    (set, get) => {
      // Define the playback status update handler here to avoid circular dependencies
      const handlePlaybackStatusUpdate = (status: any) => {
        if (!status.isLoaded) return;
        
        const currentTimeSeconds = status.positionMillis / 1000;
        const durationSeconds = status.durationMillis ? status.durationMillis / 1000 : 0;
        
        // Only update progress at key points
        if (status.isPlaying) {
          const isStart = currentTimeSeconds < 2;
          const isNearEnd = durationSeconds - currentTimeSeconds < 5;
          
          if (isStart || isNearEnd) {
            get().updateProgress(currentTimeSeconds, durationSeconds);
          }
        }
        
        // Handle playback completion
        if (status.didJustFinish) {
          get().nextSong();
        }
      };
      
      // Expose methods to handle progress
      const getProgress = () => playbackProgress;
      const updateProgress = (time: number, duration: number) => {
        playbackProgress.currentTime = time;
        playbackProgress.duration = duration;
      };
      
      return {
        ...initialState,
        
        play: async () => {
          const { currentSong } = get();
          if (!currentSong) return;
          
          try {
            await playSong(currentSong, handlePlaybackStatusUpdate);
            set({ isPlaying: true });
          } catch (error) {
            console.error('Error playing song:', error);
            set({ error: 'Failed to play song' });
          }
        },
        
        pause: async () => {
          try {
            await togglePlayback();
            set({
              isPlaying: false,
              error: null
            });
          } catch (error) {
            console.error('Error pausing playback:', error);
          }
        },
        
        playPause: async () => {
          const { isPlaying, currentSong } = get();
          
          if (!currentSong) return;
          
          try {
            if (isPlaying) {
              await togglePlayback();
              set({ isPlaying: false });
            } else {
              await initAudio();
              await playSong(currentSong, handlePlaybackStatusUpdate);
              set({ isPlaying: true });
            }
          } catch (error) {
            console.error('Error toggling playback:', error);
          }
        },
        
        setCurrentSong: async (song: Song) => {
          if (!song?.id) {
            console.error('Invalid song provided');
            return;
          }

          const currentState = get();
          
          // If it's the same song and already playing, don't restart
          if (currentState.currentSong?.id === song.id && currentState.isPlaying) {
            return;
          }

          try {
            if (currentState.isPlaying) {
              await unloadSound();
            }
            
            await initAudio();
            
            // Reset progress
            updateProgress(0, 0);
            
            // Update state
            set((state) => ({
              ...state,
              currentSong: song,
              isPlaying: false,
              error: null
            }));

            // Store in AsyncStorage in the background
            AsyncStorage.setItem('lastPlayedSong', JSON.stringify(song))
              .catch(err => {
                console.error('Error saving to local storage:', err);
              });
            
            // Start playing the song
            await playSong(song, handlePlaybackStatusUpdate);
            
            // Update playing state only if necessary
            if (!currentState.isPlaying) {
              set({ isPlaying: true });
            }
          } catch (error) {
            console.error('Error setting current song:', error);
            // Reset progress
            updateProgress(0, 0);
            
            set({ 
              error: 'Failed to play song',
              currentSong: song,
              isPlaying: false
            });
          }
        },
        
        seekToTime: async (time: number) => {
          await seekTo(time);
          updateProgress(time, playbackProgress.duration);
        },
        
        nextSong: async () => {
          const { currentSong } = get();
          const queue = get().queue || [];
          
          if (!currentSong?.id || !Array.isArray(queue) || queue.length === 0) return;
          
          const currentIndex = queue.findIndex(song => song?.id === currentSong.id);
          if (currentIndex === -1) return;
          
          const nextIndex = (currentIndex + 1) % queue.length;
          const nextSong = queue[nextIndex];
          
          if (!nextSong?.id) return;
          
          try {
            // Stop current song
            await unloadSound();
            
            // Update state
            set(() => {
              // Store in AsyncStorage
              AsyncStorage.setItem('lastPlayedSong', JSON.stringify(nextSong))
                .catch(err => {
                  console.error('Error saving to local storage:', err);
                });
              
              // Reset progress
              updateProgress(0, 0);
              
              return {
                currentSong: nextSong,
                isPlaying: false,
                error: null
              };
            });
            
            // Then play the song
            await playSong(nextSong, handlePlaybackStatusUpdate);
            set({ isPlaying: true });
          } catch (error) {
            console.error('Error playing next song:', error);
            set({ 
              error: 'Failed to play next song',
              isPlaying: false
            });
          }
        },
        
        previousSong: async () => {
          const { currentSong } = get();
          const queue = get().queue || [];
          
          if (!currentSong?.id || !Array.isArray(queue) || queue.length === 0) return;
          
          const currentIndex = queue.findIndex(song => song?.id === currentSong.id);
          if (currentIndex === -1) return;
          
          const prevIndex = (currentIndex - 1 + queue.length) % queue.length;
          const prevSong = queue[prevIndex];
          
          if (!prevSong?.id) return;
          
          try {
            // Stop current song
            await unloadSound();
            
            // Update state
            set(() => {
              // Store in AsyncStorage
              AsyncStorage.setItem('lastPlayedSong', JSON.stringify(prevSong))
                .catch(err => {
                  console.error('Error saving to local storage:', err);
                });
              
              // Reset progress
              updateProgress(0, 0);
              
              return {
                currentSong: prevSong,
                isPlaying: false,
                error: null
              };
            });
            
            // Then play the song
            await playSong(prevSong, handlePlaybackStatusUpdate);
            set({ isPlaying: true });
          } catch (error) {
            console.error('Error playing previous song:', error);
            set({ 
              error: 'Failed to play previous song',
              isPlaying: false
            });
          }
        },
        
        setSelectedLanguage: (language: string) => set({ selectedLanguage: language }),
        
        setError: (error: string | null) => set({ error }),
        
        cleanup: async () => {
          await unloadSound();
          set({ isPlaying: false });
        },
        
        setLyricsFullScreen: (isFullScreen: boolean) => set({ isLyricsFullScreen: isFullScreen }),
        
        loadSongs: async (forceRefresh = false) => {
          const { isLoading, lastFetchTime, queue } = get();
          
          // Don't fetch if already loading
          if (isLoading) return;
          
          // Check if we have cached songs and if the cache is still valid
          const cacheValid = lastFetchTime && (Date.now() - lastFetchTime < CACHE_EXPIRATION);
          
          // If we have songs in the queue and the cache is valid, and we're not forcing a refresh, return early
          if (Array.isArray(queue) && queue.length > 0 && cacheValid && !forceRefresh) {
            return;
          }
          
          let retryCount = 0;
          const maxRetries = 3;
          
          while (retryCount < maxRetries) {
            try {
              if (!Array.isArray(queue) || queue.length === 0) {
                set({ isLoading: true, error: null });
              } else {
                set({ error: null });
              }
              
              // Start fetching songs in the background
              const songs = await fetchSongs();
              
              if (Array.isArray(songs) && songs.length > 0) {
                set((state) => ({ 
                  queue: songs,
                  isLoading: false,
                  lastFetchTime: Date.now(),
                  currentSong: state.currentSong || songs[0],
                  error: null
                }));
                return; // Success - exit the retry loop
              } else {
                // Empty songs array is considered a failure
                throw new Error('No songs available');
              }
            } catch (error: any) {
              console.error(`Failed to load songs (attempt ${retryCount + 1}):`, error);
              retryCount++;
              
              if (retryCount === maxRetries) {
                // Final failure - update state with error
                set({ 
                  isLoading: false, 
                  error: error.message || 'Failed to load songs after multiple attempts',
                  lastFetchTime: Date.now()
                });
                
                // If we have cached songs, keep using them
                if (Array.isArray(queue) && queue.length > 0) {
                  console.log('Using cached songs as fallback');
                  set({ error: null });
                  return;
                }
              } else {
                // Wait before retrying (exponential backoff)
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
              }
            }
          }
        },
      };
    },
    {
      name: 'player-storage',
      storage: createJSONStorage(() => {
        // Clear the cache to remove old duration data
        AsyncStorage.removeItem(SONGS_CACHE_KEY);
        return AsyncStorage;
      }),
      partialize: (state) => ({
        currentSong: state.currentSong,
        selectedLanguage: state.selectedLanguage,
        queue: state.queue || [], // Always ensure queue is an array
        lastFetchTime: state.lastFetchTime,
      }),
    }
  )
);
