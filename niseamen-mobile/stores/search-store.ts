import { create } from 'zustand';
import { Song } from '@/types/song';
import { usePlayerStore } from './player-store';

interface SearchState {
  query: string;
  results: Song[];
  isSearching: boolean;
  
  // Actions
  setQuery: (query: string) => void;
  search: () => void;
  clearSearch: () => void;
}

export const useSearchStore = create<SearchState>()((set, get) => ({
  query: '',
  results: [],
  isSearching: false,
  
  setQuery: (query) => {
    set({ query });
    if (query.length > 2) {
      set({ isSearching: true });
      setTimeout(() => {
        get().search();
      }, 300); // Debounce search
    } else if (query.length === 0) {
      set({ results: [], isSearching: false });
    }
  },
  
  search: () => {
    const { query } = get();
    const lowerQuery = query.toLowerCase();
    const { queue } = usePlayerStore.getState();
    
    // Search in titles, song numbers, and lyrics
    const filteredSongs = queue.filter(song => {
      // Check if query is a song number
      const songNumberMatch = song.songNumber?.toString() === query;
      
      const titleMatch = song.title.toLowerCase().includes(lowerQuery);
      const artistMatch = song.artist.toLowerCase().includes(lowerQuery);
      
      // Search in all languages of lyrics
      const lyricsMatch = Object.values(song.lyrics).some(
        lyrics => lyrics.toLowerCase().includes(lowerQuery)
      );
      
      return songNumberMatch || titleMatch || artistMatch || lyricsMatch;
    });
    
    set({ results: filteredSongs, isSearching: false });
  },
  
  clearSearch: () => set({ query: '', results: [], isSearching: false }),
}));