import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User } from '@/types/song';
import { 
  signIn, 
  signOut, 
  registerUser, 
  resetPassword,
  getCurrentUser,
  subscribeToAuth<PERSON><PERSON><PERSON>,
  signInWithGoogle as firebaseSignInWithGoogle,
  persistGoogleAuthState,
  getPersistedGoogleAuthToken,
  useGoogleAuth
} from '@/services/auth';

interface UserState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  recentlyPlayed: string[]; // Array of song IDs
  lastPlayedSongId: string | null; // Cache last played song locally
  lastPlayedOffline: boolean; // Flag to indicate if last played is from offline cache
  
  // Actions
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, displayName: string) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  signInWithGoogle: (accessToken: string) => Promise<void>;
  updateLastPlayedSong: (songId: string) => Promise<void>;
  getLastPlayedSongId: () => string | null;
  updateFavoriteGenre: (genre: string) => Promise<void>;
  clearError: () => void;
  initAuth: () => void;
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: true,
      error: null,
      recentlyPlayed: [], // Initialize as empty array
      lastPlayedSongId: null,
      lastPlayedOffline: true, // Default to true since we're only using local storage
      
      initAuth: () => {
        // Subscribe to auth state changes
        const unsubscribe = subscribeToAuthChanges((user) => {
          set({ 
            user, 
            isAuthenticated: !!user,
            isLoading: false
          });
        });
        
        // Check for current user
        getCurrentUser().then(user => {
          if (user) {
            set({ user, isAuthenticated: true, isLoading: false });
          } else {
            // Check for persisted Google auth token
            getPersistedGoogleAuthToken().then(token => {
              if (token) {
                // Try to sign in with the persisted token
                firebaseSignInWithGoogle(token)
                  .then(user => {
                    if (user) {
                      set({ user, isAuthenticated: true, isLoading: false });
                    } else {
                      set({ isLoading: false });
                    }
                  })
                  .catch(error => {
                    console.error('Error signing in with persisted Google token:', error);
                    set({ isLoading: false });
                  });
              } else {
                set({ isLoading: false });
              }
            });
          }
        }).catch(error => {
          console.error('Error getting current user:', error);
          set({ isLoading: false });
        });
        
        // Return unsubscribe function (not used in this implementation)
        return unsubscribe;
      },
      
      login: async (email, password) => {
        try {
          set({ isLoading: true, error: null });
          const user = await signIn(email, password);
          set({ user, isAuthenticated: true, isLoading: false });
        } catch (error: any) {
          set({ 
            error: error.message || 'Failed to login', 
            isLoading: false 
          });
          throw error;
        }
      },
      
      register: async (email, password, displayName) => {
        try {
          set({ isLoading: true, error: null });
          const user = await registerUser(email, password, displayName);
          set({ user, isAuthenticated: true, isLoading: false });
        } catch (error: any) {
          set({ 
            error: error.message || 'Failed to register', 
            isLoading: false 
          });
          throw error;
        }
      },
      
      signInWithGoogle: async (accessToken) => {
        try {
          set({ isLoading: true, error: null });
          const user = await firebaseSignInWithGoogle(accessToken);
          
          if (user) {
            // Persist the Google access token
            await persistGoogleAuthState(accessToken);
            set({ user, isAuthenticated: true, isLoading: false });
          } else {
            set({ isLoading: false, error: 'Google sign-in failed' });
          }
        } catch (error: any) {
          set({ 
            error: error.message || 'Failed to sign in with Google', 
            isLoading: false 
          });
          throw error;
        }
      },
      
      logout: async () => {
        try {
          set({ isLoading: true, error: null });
          await signOut();
          set({ user: null, isAuthenticated: false, isLoading: false });
        } catch (error: any) {
          set({ 
            error: error.message || 'Failed to logout', 
            isLoading: false 
          });
        }
      },
      
      resetPassword: async (email) => {
        try {
          set({ isLoading: true, error: null });
          await resetPassword(email);
          set({ isLoading: false });
        } catch (error: any) {
          set({ 
            error: error.message || 'Failed to reset password', 
            isLoading: false 
          });
          throw error;
        }
      },
      
      updateLastPlayedSong: async (songId) => {
        try {
          const currentState = get();
          const currentRecentlyPlayed = currentState.recentlyPlayed || [];
          
          // If this song is already the most recent, don't update
          if (currentState.lastPlayedSongId === songId && 
              currentRecentlyPlayed[0] === songId) {
            return;
          }
          
          let newRecentlyPlayed = [...currentRecentlyPlayed];
          newRecentlyPlayed = newRecentlyPlayed.filter(id => id !== songId);
          newRecentlyPlayed.unshift(songId);
          
          if (newRecentlyPlayed.length > 10) {
            newRecentlyPlayed = newRecentlyPlayed.slice(0, 10);
          }
          
          // Batch update state
          set((state) => ({
            ...state,
            recentlyPlayed: newRecentlyPlayed,
            lastPlayedSongId: songId,
          }));
          
          // Store in AsyncStorage in the background
          Promise.all([
            AsyncStorage.setItem('lastPlayedSongId', songId),
            AsyncStorage.setItem('recentlyPlayed', JSON.stringify(newRecentlyPlayed))
          ]).catch(error => {
            console.error('Error storing recent songs:', error);
          });
        } catch (error) {
          console.error('Error updating last played song:', error);
        }
      },
      
      getLastPlayedSongId: () => {
        return get().lastPlayedSongId;
      },
      
      updateFavoriteGenre: async (genre) => {
        const { user } = get();
        if (!user) return;
        
        try {
          // Update local state only
          set({
            user: { ...user, favoriteGenre: genre }
          });
          
          // Store in AsyncStorage
          await AsyncStorage.setItem('favoriteGenre', genre);
        } catch (error) {
          console.error('Error updating favorite genre:', error);
        }
      },
      
      clearError: () => set({ error: null }),
    }),
    {
      name: 'user-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        recentlyPlayed: state.recentlyPlayed || [],
        lastPlayedSongId: state.lastPlayedSongId
      }),
    }
  )
);
