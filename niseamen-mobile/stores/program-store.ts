import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MonthProgram, ProgramState, WeekProgram } from '@/types/program';
import { 
  fetchCurrentMonthProgram, 
  getCurrentWeekProgram, 
  getTodaysFeaturedSong 
} from '@/services/program';

interface ProgramStore extends ProgramState {
  // Actions
  loadProgram: (forceRefresh?: boolean) => Promise<void>;
  setError: (error: string | null) => void;
}

export const useProgramStore = create<ProgramStore>()(
  persist(
    (set, get) => ({
      currentFeaturedSong: null,
      currentWeek: null,
      currentMonth: null,
      isLoading: false,
      error: null,
      
      loadProgram: async (forceRefresh = false) => {
        const { isLoading, currentMonth, currentFeaturedSong } = get();
        
        // Don't fetch if already loading
        if (isLoading) return;
        
        // If we have data and not forcing refresh, return early
        if (currentMonth && currentFeaturedSong && !forceRefresh) {
          return;
        }
        
        try {
          set({ isLoading: true, error: null });
          
          // If forceRefresh is true, clear the featured song cache
          if (forceRefresh) {
            await AsyncStorage.removeItem('featured_song_cache');
          }
          
          // Fetch all program data
          const [monthProgram, weekProgram, featuredSong] = await Promise.all([
            fetchCurrentMonthProgram(),
            getCurrentWeekProgram(),
            getTodaysFeaturedSong()
          ]);
          
          set({ 
            currentMonth: monthProgram,
            currentWeek: weekProgram,
            currentFeaturedSong: featuredSong,
            isLoading: false
          });
        } catch (error: any) {
          console.error('Failed to load program:', error);
          set({ 
            isLoading: false, 
            error: error.message || 'Failed to load program'
          });
        }
      },
      
      setError: (error) => set({ error }),
    }),
    {
      name: 'program-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        currentMonth: state.currentMonth,
        currentWeek: state.currentWeek,
        currentFeaturedSong: state.currentFeaturedSong,
      }),
    }
  )
);
