export interface Song {
  id: string;
  title: string;
  artist: string;
  imageUrl?: string;
  audioUrl: string;
  duration?: number;
  lyrics: {
    [language: string]: string;
  };
  songNumber?: number;
}

export interface User {
  id: string;
  displayName: string;
  email: string;
  photoUrl: string;
  lastPlayedSongId?: string;
  favoriteGenre?: string;
}

export type Language = 'goun' | 'french';
