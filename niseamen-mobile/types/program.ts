import { Timestamp } from 'firebase/firestore';

// Type for Firestore timestamp objects that have a toDate() method
export interface FirestoreTimestamp {
  toDate: () => Date;
  seconds: number;
  nanoseconds: number;
}

export interface WeekProgram {
  weekNumber: number;
  startDate: Date | string | FirestoreTimestamp;
  songs: number[];
}

export interface MonthProgram {
  month: number;
  year: number;
  weeks: WeekProgram[];
}

export interface ProgramState {
  currentFeaturedSong: number | null;
  currentWeek: WeekProgram | null;
  currentMonth: MonthProgram | null;
  isLoading: boolean;
  error: string | null;
}
