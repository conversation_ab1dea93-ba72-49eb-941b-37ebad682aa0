import { initializeApp } from 'firebase/app';
import { getAuth, initializeAuth, getReactNativePersistence } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyC3zU2TFyASwkVHKlNFf57CJhp_9wnwRVU",
  authDomain: "niseamen.firebaseapp.com",
  databaseURL: "https://niseamen.firebaseio.com",
  projectId: "niseamen",
  storageBucket: "niseamen.appspot.com",
  messagingSenderId: "578866985713",
  appId: "1:578866985713:web:05df1380459293b50bc750"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Auth with platform-specific persistence
const auth = Platform.select({
  web: () => getAuth(app),
  default: () => initializeAuth(app, {
    persistence: getReactNativePersistence(AsyncStorage)
  })
})();

// Initialize Firebase services
const firestore = getFirestore(app);
const storage = getStorage(app);

export { auth, firestore, storage };
export default app;
