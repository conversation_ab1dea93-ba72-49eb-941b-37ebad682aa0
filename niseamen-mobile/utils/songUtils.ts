/**
 * List of special song numbers that should display with "bis" notation
 */
const bisSongs = [46]; // Add all relevant song numbers that have "bis" versions

/**
 * Format a song number, adding "bis" notation if applicable
 */
export function formatSongNumber(songNumber: number): string {
  // Check if this song number should display as a "bis" version
  if (bisSongs.includes(songNumber)) {
    return `${songNumber}bis`;
  }
  return songNumber.toString();
}
