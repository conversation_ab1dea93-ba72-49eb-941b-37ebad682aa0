import React from 'react';
import { View, Text, StyleSheet, Platform, Pressable } from 'react-native';
import Colors from '@/constants/colors';
import * as SplashScreen from 'expo-splash-screen';

interface Props {
  children: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  retryCount: number;
}

const MAX_RETRIES = 3;
const RETRY_DELAY = 2000; // 2 seconds

export class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      retryCount: 0 
    };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log error to your error reporting service
    console.error('Error caught by boundary:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
      hasError: true
    });

    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Hide splash screen if it's still visible
    SplashScreen.hideAsync().catch(console.error);
  }

  handleRetry = async () => {
    if (this.state.retryCount >= MAX_RETRIES) {
      console.log('Max retries reached');
      return;
    }

    this.setState(prevState => ({
      retryCount: prevState.retryCount + 1,
      hasError: false,
      error: null,
      errorInfo: null
    }));

    // Add a small delay before retry
    await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
  };

  render() {
    if (this.state.hasError) {
      const canRetry = this.state.retryCount < MAX_RETRIES;
      
      return (
        <View style={styles.container}>
          <View style={styles.content}>
            <Text style={styles.title}>Something went wrong</Text>
            
            <Text style={styles.subtitle}>
              {this.state.error?.message || 'An unexpected error occurred'}
            </Text>
            
            {canRetry && (
              <Pressable 
                style={styles.retryButton} 
                onPress={this.handleRetry}
              >
                <Text style={styles.retryText}>
                  Retry ({MAX_RETRIES - this.state.retryCount} attempts remaining)
                </Text>
              </Pressable>
            )}

            {!canRetry && (
              <Text style={styles.maxRetriesText}>
                Please try restarting the app
              </Text>
            )}

            {__DEV__ && this.state.errorInfo && (
              <Text style={styles.devError}>
                {this.state.errorInfo.componentStack}
              </Text>
            )}
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    color: Colors.dark.text,
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.dark.textSecondary,
    marginBottom: 24,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: Colors.dark.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  retryText: {
    color: Colors.dark.background,
    fontSize: 16,
    fontWeight: '600',
  },
  maxRetriesText: {
    color: Colors.dark.textSecondary,
    marginTop: 16,
    fontSize: 14,
  },
  devError: {
    color: Colors.dark.textTertiary,
    fontSize: 12,
    marginTop: 20,
    padding: 10,
  },
});

export default ErrorBoundary;
