import React from "react";
import { Tabs } from "expo-router";
import { Home, Search, User } from "lucide-react-native";
import { View, StyleSheet, ActivityIndicator } from "react-native";
import { usePlayerStore } from "@/stores/player-store";
import Colors from "@/constants/colors";
import MiniPlayer from "@/components/MiniPlayer";

export default function TabLayout() {
  const { isLoading, error } = usePlayerStore();

  return (
    <View style={[styles.container, isLoading && styles.loadingContainer]}>
      {isLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={Colors.dark.primary} />
        </View>
      )}
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: Colors.dark.primary,
          tabBarInactiveTintColor: Colors.dark.textSecondary,
          tabBarStyle: {
            backgroundColor: Colors.dark.background,
            borderTopColor: 'rgba(226, 177, 60, 0.3)',
            borderTopWidth: 1,
            height: 60,
            paddingBottom: 8,
          },
          headerStyle: {
            backgroundColor: Colors.dark.background,
          },
          headerTintColor: Colors.dark.text,
          tabBarLabelStyle: {
            fontSize: 12,
            marginBottom: 4,
          },
        }}
      >
        <Tabs.Screen
          name="index"
          options={{
            title: "Home",
            tabBarIcon: ({ color }) => <Home size={24} color={color} />,
          }}
        />
        <Tabs.Screen
          name="search"
          options={{
            title: "Search",
            tabBarIcon: ({ color }) => <Search size={24} color={color} />,
          }}
        />
        <Tabs.Screen
          name="profile"
          options={{
            title: "Profile",
            tabBarIcon: ({ color }) => <User size={24} color={color} />,
            headerShown: false,
          }}
        />
      </Tabs>
      <MiniPlayer />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
  loadingContainer: {
    opacity: 0.7,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: Colors.dark.background,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
});
