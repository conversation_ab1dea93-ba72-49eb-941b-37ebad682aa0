import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, FlatList, ActivityIndicator, RefreshControl, Image } from 'react-native';
import { Stack } from 'expo-router';
import { usePlayerStore } from '@/stores/player-store';
import { useUserStore } from '@/stores/user-store';
import { useProgramStore } from '@/stores/program-store';
import Colors from '@/constants/colors';
import { Music, BookOpen } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import FeaturedSong from '@/components/FeaturedSong';
import WeeklyProgram from '@/components/WeeklyProgram';
import MonthlyProgram from '@/components/MonthlyProgram';
import SongItem from '@/components/SongItem';
import { Song } from '@/types/song';

export default function HomeScreen() {
  const loadSongs = usePlayerStore(state => state.loadSongs);
const songsLoading = usePlayerStore(state => state.isLoading);
const queue = usePlayerStore(state => state.queue);
  const { loadProgram, isLoading: programLoading } = useProgramStore();
  const { user, recentlyPlayed } = useUserStore();
  const [refreshing, setRefreshing] = useState(false);
  
  // Load songs and program data
  useEffect(() => {
    loadSongs();
    loadProgram();
  }, []);
  
  // Handle pull-to-refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      loadSongs(true),
      loadProgram(true)
    ]);
    setRefreshing(false);
  };
  
  // Get recently played songs from user store
  const getRecentSongs = () => {
    if (!recentlyPlayed || recentlyPlayed.length === 0) return [];
    
    return recentlyPlayed
      .map(id => queue.find(song => song.id === id))
      .filter(Boolean) as Song[];
  };
  
  const recentSongs = getRecentSongs();
  
  // Show loading indicator only if we have no songs at all
  const isLoading = (songsLoading || programLoading) && queue.length === 0;
  
  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  // Get user's first name safely
  const getUserFirstName = () => {
    if (user && user.displayName) {
      const nameParts = user.displayName.split(' ');
      return nameParts[0];
    }
    return '';
  };
  
  return (
    <View style={styles.container}>
      <Stack.Screen options={{ 
        headerShown: false
      }} />
      
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.dark.primary} />
          <Text style={styles.loadingText}>Loading hymns...</Text>
        </View>
      ) : (
        <FlatList
          data={[]}
          keyExtractor={(item, index) => index.toString()}
          renderItem={() => null}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[Colors.dark.primary]}
              tintColor={Colors.dark.primary}
            />
          }
          ListHeaderComponent={() => (
            <>
              <LinearGradient
                colors={[Colors.dark.primary, 'rgba(14, 23, 42, 0.9)']}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
                style={styles.headerGradient}
              >
                <View style={styles.headerContent}>
                  <View style={styles.headerTitleContainer}>
                    <Text style={styles.appTitle}>NiseAmen</Text>
                    <View style={styles.musicIconContainer}>
                      <BookOpen size={20} color={Colors.dark.background} />
                    </View>
                  </View>
                  
                  <Text style={styles.greeting}>
                    {getGreeting()}{getUserFirstName() ? `, ${getUserFirstName()}` : ''}
                  </Text>
                  
                  <Text style={styles.hallelujah}>Hallelujah!</Text>
                </View>
              </LinearGradient>
              
              {(songsLoading || programLoading) && (
                <View style={styles.refreshingContainer}>
                  <ActivityIndicator size="small" color={Colors.dark.primary} />
                  <Text style={styles.refreshingText}>Refreshing content...</Text>
                </View>
              )}
              
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Today's Hymn</Text>
                <FeaturedSong />
              </View>
              
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Recently Played</Text>
                <View style={styles.recentContainer}>
                  {recentSongs.length > 0 ? (
                    recentSongs.map((song, index) => (
                      <SongItem key={index} song={song} compact={true} />
                    ))
                  ) : (
                    <View style={styles.emptyRecentContainer}>
                      <Music size={24} color={Colors.dark.textTertiary} />
                      <Text style={styles.emptyRecentText}>
                        No recently played hymns
                      </Text>
                    </View>
                  )}
                </View>
              </View>
              
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>This Week</Text>
                <WeeklyProgram />
              </View>
              
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Monthly Program</Text>
                <MonthlyProgram />
              </View>
            </>
          )}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
  headerGradient: {
    paddingTop: 60, // Space for status bar
    paddingBottom: 20,
  },
  headerContent: {
    paddingHorizontal: 16,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  appTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.dark.background,
    marginRight: 8,
  },
  musicIconContainer: {
    backgroundColor: 'rgba(15, 23, 42, 0.3)',
    borderRadius: 12,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  greeting: {
    fontSize: 18,
    color: Colors.dark.background,
    marginTop: 8,
    opacity: 0.8,
  },
  hallelujah: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.dark.background,
    marginTop: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.dark.background,
  },
  loadingText: {
    color: Colors.dark.text,
    marginTop: 16,
    fontSize: 16,
  },
  refreshingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    backgroundColor: Colors.dark.surface,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
  },
  refreshingText: {
    color: Colors.dark.text,
    marginLeft: 8,
    fontSize: 14,
  },
  listContent: {
    paddingBottom: 140,
  },
  section: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.dark.text,
    marginTop: 24,
    marginBottom: 16,
  },
  recentContainer: {
    marginBottom: 16,
  },
  emptyRecentContainer: {
    backgroundColor: Colors.dark.surface,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyRecentText: {
    color: Colors.dark.textSecondary,
    marginTop: 8,
    fontSize: 14,
  },
});