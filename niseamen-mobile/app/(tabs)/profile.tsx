import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ProfileHeader from '@/components/ProfileHeader';
import SongItem from '@/components/SongItem';
import { useUserStore } from '@/stores/user-store';
import { usePlayerStore } from '@/stores/player-store';
import Colors from '@/constants/colors';
import LoginScreen from '@/components/LoginScreen';
import { BookOpen, Music } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

export default function ProfileScreen() {
  const { user, isAuthenticated, initAuth } = useUserStore();
  const { currentSong, queue } = usePlayerStore();
  
  // Ensure auth is initialized
  useEffect(() => {
    initAuth();
  }, []);
  
  if (!isAuthenticated) {
    return <LoginScreen />;
  }
  
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <LinearGradient
          colors={[Colors.dark.primary, Colors.dark.background]}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 0.2 }}
          style={styles.headerGradient}
        >
          <Text style={styles.headerTitle}>Profile</Text>
          <ProfileHeader />
        </LinearGradient>
        
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Music size={18} color={Colors.dark.primary} />
            <Text style={styles.sectionTitle}>Now Playing</Text>
          </View>
          
          {currentSong ? (
            <SongItem song={currentSong} />
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No hymn currently playing</Text>
            </View>
          )}
        </View>
        
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <BookOpen size={18} color={Colors.dark.primary} />
            <Text style={styles.sectionTitle}>App Information</Text>
          </View>
          
          <LinearGradient
            colors={[Colors.dark.surface, 'rgba(30, 41, 59, 0.8)']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.infoCard}
          >
            <View style={styles.appLogoContainer}>
              <Text style={styles.appLogoText}>NA</Text>
            </View>
            <Text style={styles.infoTitle}>NiseAmen</Text>
            <Text style={styles.infoVersion}>Version 1.0.0</Text>
            <Text style={styles.infoDescription}>
              A spiritual hymnal app for religious songs with multilingual lyrics support.
            </Text>
          </LinearGradient>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
  scrollContent: {
    paddingBottom: 140,
  },
  headerGradient: {
    paddingTop: 16,
    paddingBottom: 24,
    paddingHorizontal: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.dark.background,
    marginBottom: 16,
  },
  section: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.dark.text,
    marginLeft: 8,
  },
  emptyContainer: {
    backgroundColor: Colors.dark.surface,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(248, 250, 252, 0.05)',
  },
  emptyText: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  infoCard: {
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(226, 177, 60, 0.3)',
  },
  appLogoContainer: {
    width: 64,
    height: 64,
    borderRadius: 16,
    backgroundColor: Colors.dark.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  appLogoText: {
    color: Colors.dark.background,
    fontSize: 24,
    fontWeight: 'bold',
  },
  infoTitle: {
    color: Colors.dark.text,
    fontSize: 20,
    fontWeight: 'bold',
  },
  infoVersion: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    marginTop: 4,
  },
  infoDescription: {
    color: Colors.dark.text,
    fontSize: 14,
    marginTop: 16,
    lineHeight: 20,
    textAlign: 'center',
  },
});
