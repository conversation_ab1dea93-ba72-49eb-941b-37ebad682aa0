import React, { useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, ActivityIndicator } from 'react-native';
import { Stack } from 'expo-router';
import { useSearchStore } from '@/stores/search-store';
import { usePlayerStore } from '@/stores/player-store';
import SearchBar from '@/components/SearchBar';
import SongItem from '@/components/SongItem';
import Colors from '@/constants/colors';
import { BookOpen, Search } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

export default function SearchScreen() {
  const { query, results, isSearching, setQuery } = useSearchStore();
  const { queue, isLoading } = usePlayerStore();
  
  // Clear search when component unmounts
  useEffect(() => {
    return () => {
      setQuery('');
    };
  }, []);
  
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.dark.primary} />
        <Text style={styles.loadingText}>Loading hymns...</Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <Stack.Screen options={{ 
        title: 'Search',
        headerShown: false,
      }} />
      
      <LinearGradient
        colors={[Colors.dark.primary, Colors.dark.background]}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 0.2 }}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Find Hymns</Text>
          <SearchBar />
        </View>
      </LinearGradient>
      
      {isSearching ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator color={Colors.dark.primary} size="large" />
        </View>
      ) : (
        <FlatList
          data={query.length > 0 ? results : queue}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <SongItem 
              song={item} 
              showLyrics={true}
              searchQuery={query}
            />
          )}
          contentContainerStyle={styles.listContent}
          ListHeaderComponent={() => (
            <View style={styles.resultsHeader}>
              {query.length > 0 ? (
                <Text style={styles.resultsText}>
                  {results.length} result{results.length !== 1 ? 's' : ''} for "{query}"
                </Text>
              ) : (
                <View style={styles.allHymnsHeader}>
                  <BookOpen size={18} color={Colors.dark.primary} />
                  <Text style={styles.allHymnsText}>All Hymns</Text>
                </View>
              )}
            </View>
          )}
          ListEmptyComponent={() => (
            <View style={styles.emptyContainer}>
              <Search size={40} color={Colors.dark.textTertiary} />
              <Text style={styles.emptyText}>
                No hymns found matching "{query}"
              </Text>
            </View>
          )}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
  headerGradient: {
    paddingTop: 50,
    paddingBottom: 16,
  },
  headerContent: {
    paddingHorizontal: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.dark.background,
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: Colors.dark.text,
    marginTop: 16,
    fontSize: 16,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 140,
  },
  resultsHeader: {
    marginVertical: 16,
  },
  resultsText: {
    fontSize: 16,
    color: Colors.dark.textSecondary,
  },
  allHymnsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  allHymnsText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.dark.text,
    marginLeft: 8,
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyText: {
    color: Colors.dark.textSecondary,
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
});