import { useEffect, useState } from "react";
import { Platform } from "react-native";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import * as Updates from "expo-updates";
import { StatusBar } from "expo-status-bar";
import { View, StyleSheet } from "react-native";
import { ErrorBoundary } from "./error-boundary";
import Colors from "@/constants/colors";
import { useUserStore } from "@/stores/user-store";
import { usePlayerStore } from "@/stores/player-store";
import { initAudio } from "@/services/audio";

export const unstable_settings = {
  initialRouteName: "(tabs)",
  parent: "(tabs)"
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const { initAuth } = useUserStore();
  const { loadSongs, cleanup } = usePlayerStore();
  const [isCheckingUpdate, setIsCheckingUpdate] = useState(false);

  // Check and apply updates immediately
  async function checkForUpdates() {
    if (isCheckingUpdate) return;
    
    try {
      setIsCheckingUpdate(true);
      const update = await Updates.checkForUpdateAsync();
      
      if (update.isAvailable) {
        await Updates.fetchUpdateAsync();
        // Immediately reload without showing an alert
        await Updates.reloadAsync();
      }
    } catch (error) {
      console.log('Error checking for updates:', error);
    } finally {
      setIsCheckingUpdate(false);
    }
  }

  useEffect(() => {
    async function prepare() {
      try {
        // Check for updates first
        await checkForUpdates();
        
        // Initialize authentication
        await initAuth();
        
        // Initialize audio system
        await initAudio();
        
        // Load songs with a timeout
        const loadSongsWithTimeout = async () => {
          try {
            await Promise.race([
              loadSongs(),
              new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Songs loading timeout')), 10000)
              )
            ]);
          } catch (error) {
            console.warn('Song loading issue:', error);
            // Continue even if songs fail to load - they can load later
          }
        };

        await loadSongsWithTimeout();
        
      } catch (error) {
        console.error('Preparation failed:', error);
      } finally {
        // Hide splash screen
        try {
          await SplashScreen.hideAsync();
        } catch (error) {
          console.error('Error hiding splash screen:', error);
        }
      }
    }

    prepare();

    // Cleanup on unmount
    return () => {
      cleanup();
    };
  }, []);

  return (
    <ErrorBoundary>
      <View style={styles.container}>
        <StatusBar style="light" />
        <RootLayoutNav />
      </View>
    </ErrorBoundary>
  );
}

function RootLayoutNav() {
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: Colors.dark.background,
        },
        headerTintColor: Colors.dark.text,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        contentStyle: {
          backgroundColor: Colors.dark.background,
        },
      }}
    >
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
      <Stack.Screen
        name="player"
        options={{
          presentation: 'containedModal',
          headerTitle: 'Now Playing',
          headerBackTitle: 'Back',
          animation: 'fade',
          gestureEnabled: false
        }}
      />
    </Stack>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
});
