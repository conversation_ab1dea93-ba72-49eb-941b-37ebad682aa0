import React, { useEffect, useState, useRef } from 'react';
import { View, Text, StyleSheet, Image, ScrollView, Animated, Platform, Pressable, Dimensions } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { usePlayerStore } from '@/stores/player-store';
import PlayerControls from '@/components/PlayerControls';
import ProgressBar from '@/components/ProgressBar';
import LanguageSelector from '@/components/LanguageSelector';
import Colors from '@/constants/colors';
import { LinearGradient } from 'expo-linear-gradient';
import { BookOpen, ChevronUp, X } from 'lucide-react-native';
import { StatusBar } from 'expo-status-bar';
import { BlurView } from 'expo-blur';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Song } from '@/types/song';

const { width, height } = Dimensions.get('window');
const DEFAULT_IMAGE = require('@/assets/images/icon.png');

export default function PlayerScreen() {
  const { 
    currentSong, 
    isPlaying,
    selectedLanguage, 
    isLyricsFullScreen, 
    setLyricsFullScreen, 
    queue, 
    setCurrentSong,
    getProgress 
  } = usePlayerStore();

  const [spinValue] = useState(new Animated.Value(0));
  const scrollViewRef = useRef<ScrollView>(null);
  const router = useRouter();
  
  // Initialize player with last played song from local storage
  useEffect(() => {
    const loadLastPlayedSong = async () => {
      try {
        if (!currentSong && Array.isArray(queue) && queue.length > 0) {
          const lastPlayedJson = await AsyncStorage.getItem('lastPlayedSong');
          if (lastPlayedJson) {
            const lastPlayed = JSON.parse(lastPlayedJson) as Song;
            const lastPlayedSong = queue.find(song => song?.id === lastPlayed?.id);
            if (lastPlayedSong?.id) {
              setCurrentSong(lastPlayedSong);
            }
          }
        }
      } catch (error) {
        console.error('Error loading last played song:', error);
      }
    };
    
    loadLastPlayedSong();
  }, [queue, currentSong, setCurrentSong]);
  
  // Rotate animation for album art
  useEffect(() => {
    let spinAnimation: Animated.CompositeAnimation;
    
    if (isPlaying) {
      spinAnimation = Animated.loop(
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 10000,
          useNativeDriver: Platform.OS !== 'web',
        })
      );
      spinAnimation.start();
    }
    
    return () => {
      if (spinAnimation) {
        spinAnimation.stop();
      }
    };
  }, [isPlaying, spinValue]);
  
  // Spin interpolation
  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  
  if (!currentSong) {
    return (
      <View style={styles.container}>
        <Text style={styles.noSongText}>No hymn selected</Text>
      </View>
    );
  }
  
  // Get lyrics for selected language
  const lyrics = currentSong.lyrics[selectedLanguage] || '';

  const renderLyricsFullScreen = () => (
    <View style={styles.fullScreenContainer}>
      <StatusBar style="light" />
      <LinearGradient
        colors={[Colors.dark.background, Colors.dark.surface]}
        style={styles.fullScreenGradient}
      >
        <View style={styles.fullScreenHeader}>
          <View style={styles.fullScreenHeaderContent}>
            <View>
              <Text style={styles.fullScreenTitle}>{currentSong?.title || 'Unknown Title'}</Text>
              <Text style={styles.fullScreenArtist}>{currentSong?.artist || 'Unknown Artist'}</Text>
            </View>
            <LanguageSelector compact={true} />
          </View>
          
          <Pressable
            style={styles.closeButton}
            onPress={toggleLyricsFullScreen}
          >
            <X size={24} color={Colors.dark.text} />
          </Pressable>
        </View>
        
        <ScrollView
          ref={scrollViewRef}
          style={styles.fullScreenLyricsScroll}
          contentContainerStyle={styles.fullScreenLyricsContent}
          showsVerticalScrollIndicator={false}
        >
          <Text style={styles.fullScreenLyricsText}>
            {lyrics || `No lyrics available in ${selectedLanguage}`}
          </Text>
        </ScrollView>
        
        <View style={styles.miniPlayerContainer}>
          {Platform.OS !== 'web' ? (
            <BlurView intensity={80} tint="dark" style={styles.miniPlayerBlur}>
              <View style={styles.miniPlayer}>
                <Image
                  source={currentSong?.imageUrl ? {
                    uri: currentSong.imageUrl,
                    cache: 'force-cache'
                  } : DEFAULT_IMAGE}
                  style={styles.miniPlayerImage}
                />
                <View style={styles.miniPlayerControls}>
                  <PlayerControls size="small" minimal={true} />
                </View>
              </View>
            </BlurView>
          ) : (
            <View style={[styles.miniPlayerBlur, { backgroundColor: 'rgba(15, 23, 42, 0.8)' }]}>
              <View style={styles.miniPlayer}>
                <Image
                  source={currentSong?.imageUrl ? {
                    uri: currentSong.imageUrl,
                    cache: 'force-cache'
                  } : DEFAULT_IMAGE}
                  style={styles.miniPlayerImage}
                />
                <View style={styles.miniPlayerControls}>
                  <PlayerControls size="small" minimal={true} />
                </View>
              </View>
            </View>
          )}
        </View>
      </LinearGradient>
    </View>
  );

  const toggleLyricsFullScreen = () => {
    setLyricsFullScreen(!isLyricsFullScreen);
    // Scroll to top when opening full screen lyrics
    if (!isLyricsFullScreen && scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ y: 0, animated: false });
    }
  };

  if (isLyricsFullScreen) {
    return renderLyricsFullScreen();
  }
  
  return (
    <View style={styles.container}>
      <Stack.Screen
        options={{
          title: 'Now Playing',
          headerTitleStyle: {
            fontWeight: 'bold',
            color: Colors.dark.text,
          },
          headerStyle: {
            backgroundColor: Colors.dark.background,
          },
          headerTintColor: Colors.dark.primary,
          headerLeft: () => (
            <Pressable
              onPress={() => {
                if (isPlaying) {
                  // Just close the modal if playing
                  router.back();
                } else {
                  // Stop and go back
                  router.replace('/(tabs)');
                }
              }}
              style={styles.backButton}
            >
              <Text style={{ color: Colors.dark.primary }}>
                {isPlaying ? 'Close' : 'Back'}
              </Text>
            </Pressable>
          ),
        }}
      />
      
      <LinearGradient
        colors={[Colors.dark.background, Colors.dark.surface]}
        style={styles.gradientContainer}
      >
        <View style={styles.playerContainer}>
          <View style={styles.songNumberContainer}>
            <Text style={styles.songNumber}>
              #{currentSong?.songNumber || ''}
            </Text>
          </View>
          
          <View style={styles.albumContainer}>
            {Platform.OS !== 'web' ? (
              <Animated.Image 
                source={currentSong?.imageUrl ? {
                  uri: currentSong.imageUrl,
                  cache: 'force-cache'
                } : DEFAULT_IMAGE}
                style={[styles.albumArt, { transform: [{ rotate: spin }] }]} 
              />
            ) : (
              <Image 
                source={currentSong?.imageUrl ? {
                  uri: currentSong.imageUrl,
                  cache: 'force-cache'
                } : DEFAULT_IMAGE}
                style={styles.albumArt} 
              />
            )}
          </View>
          
          <View style={styles.songInfoContainer}>
            <Text style={styles.songTitle}>{currentSong?.title || 'Unknown Title'}</Text>
            <Text style={styles.artistName}>{currentSong?.artist || 'Unknown Artist'}</Text>
          </View>
          
          <ProgressBar />
          
          <View style={styles.controlsContainer}>
            <PlayerControls size="large" />
          </View>
        </View>
        
        <View style={styles.lyricsContainer}>
          <View style={styles.lyricsHeader}>
            <View style={styles.lyricsHeaderLeft}>
              <BookOpen size={20} color={Colors.dark.primary} />
              <Text style={styles.lyricsTitle}>Lyrics</Text>
            </View>
            <View style={styles.lyricsHeaderRight}>
              <LanguageSelector />
              <Pressable 
                style={styles.expandButton} 
                onPress={toggleLyricsFullScreen}
              >
                <ChevronUp size={20} color={Colors.dark.primary} />
              </Pressable>
            </View>
          </View>
          
          <ScrollView 
            style={styles.lyricsScroll}
            contentContainerStyle={styles.lyricsContent}
            showsVerticalScrollIndicator={false}
          >
            <Text style={styles.lyricsText}>
              {lyrics || `No lyrics available in ${selectedLanguage}`}
            </Text>
          </ScrollView>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  backButton: {
    padding: 8,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
  gradientContainer: {
    flex: 1,
  },
  playerContainer: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  songNumberContainer: {
    backgroundColor: 'rgba(226, 177, 60, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    marginBottom: 16,
  },
  songNumber: {
    color: Colors.dark.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  albumContainer: {
    width: 220,
    height: 220,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: 'rgba(226, 177, 60, 0.3)',
  },
  albumArt: {
    width: '100%',
    height: '100%',
  },
  songInfoContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  songTitle: {
    color: Colors.dark.text,
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  artistName: {
    color: Colors.dark.textSecondary,
    fontSize: 16,
    marginTop: 4,
    textAlign: 'center',
  },
  controlsContainer: {
    marginTop: 16,
    marginBottom: 24,
    width: '100%',
  },
  lyricsContainer: {
    flex: 1,
    backgroundColor: Colors.dark.surface,
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    paddingTop: 24,
    paddingHorizontal: 24,
    borderTopWidth: 1,
    borderTopColor: 'rgba(226, 177, 60, 0.3)',
  },
  lyricsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  lyricsHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  lyricsHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  lyricsTitle: {
    color: Colors.dark.text,
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  expandButton: {
    marginLeft: 12,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(226, 177, 60, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  lyricsScroll: {
    flex: 1,
  },
  lyricsContent: {
    paddingBottom: 32,
  },
  lyricsText: {
    color: Colors.dark.text,
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
  },
  noSongText: {
    color: Colors.dark.textSecondary,
    fontSize: 18,
    textAlign: 'center',
    marginTop: 40,
  },
  fullScreenContainer: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
  fullScreenGradient: {
    flex: 1,
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
  },
  fullScreenHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(226, 177, 60, 0.2)',
  },
  fullScreenHeaderContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginRight: 16,
  },
  fullScreenTitle: {
    color: Colors.dark.text,
    fontSize: 20,
    fontWeight: 'bold',
  },
  fullScreenArtist: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    marginTop: 4,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(248, 250, 252, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenLyricsScroll: {
    flex: 1,
  },
  fullScreenLyricsContent: {
    paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 100,
  },
  fullScreenLyricsText: {
    color: Colors.dark.text,
    fontSize: 18,
    lineHeight: 32,
    textAlign: 'center',
  },
  miniPlayerContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  miniPlayerBlur: {
    overflow: 'hidden',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  miniPlayer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  miniPlayerImage: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 16,
  },
  miniPlayerControls: {
    flex: 1,
  },
});
