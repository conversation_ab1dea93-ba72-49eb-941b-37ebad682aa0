import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Pressable, ActivityIndicator, ScrollView } from 'react-native';
import { usePlayerStore } from '@/stores/player-store';
import { useProgramStore } from '@/stores/program-store';
import { formatDayOfWeek } from '@/services/program';
import Colors from '@/constants/colors';
import { Calendar, Music, ArrowLeft } from 'lucide-react-native';
import { fetchSongById } from '@/services/songs';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Song } from '@/types/song';
import { FirestoreTimestamp, WeekProgram } from '@/types/program';

export default function WeekDetailsScreen() {
  const { queue, setCurrentSong } = usePlayerStore();
  const { currentMonth } = useProgramStore();
  const [isLoadingSongs, setIsLoadingSongs] = useState(false);
  const [weekSongs, setWeekSongs] = useState<Array<Song | null>>([]);
  const [selectedWeek, setSelectedWeek] = useState<WeekProgram | null>(null);
  const router = useRouter();
  const params = useLocalSearchParams();
  const weekNumber = params.weekNumber ? parseInt(params.weekNumber as string) : null;
  
  // Find the selected week from the current month based on weekNumber
  useEffect(() => {
    if (currentMonth && weekNumber !== null) {
      const week = currentMonth.weeks.find(w => w.weekNumber === weekNumber);
      if (week) {
        setSelectedWeek(week);
      }
    }
  }, [currentMonth, weekNumber]);
  
  // Load songs for the week that might not be in the queue
  useEffect(() => {
    const loadWeekSongs = async () => {
      if (!selectedWeek || !selectedWeek.songs || selectedWeek.songs.length === 0) {
        return;
      }
      
      setIsLoadingSongs(true);
      
      try {
        const songsToLoad: Array<Song | null> = [];
        
        // For each song number in the week
        for (let i = 0; i < selectedWeek.songs.length; i++) {
          const songNumber = selectedWeek.songs[i];
          
          // First check if it's in the queue
          const songInQueue = queue.find(song => song.songNumber === songNumber);
          
          if (songInQueue) {
            songsToLoad[i] = songInQueue;
          } else {
            // If not in queue, try to fetch it
            try {
              const songId = songNumber.toString().padStart(3, '0');
              const song = await fetchSongById(songId);
              if (song) {
                songsToLoad[i] = song;
              } else {
                songsToLoad[i] = null;
              }
            } catch (error) {
              console.error(`Error fetching song #${songNumber}:`, error);
              songsToLoad[i] = null;
            }
          }
        }
        
        setWeekSongs(songsToLoad);
      } catch (error) {
        console.error('Error loading week songs:', error);
      } finally {
        setIsLoadingSongs(false);
      }
    };
    
    loadWeekSongs();
  }, [selectedWeek, queue]);
  
  // Handle song press with proper event handling
  const handleSongPress = (song: Song | null) => {
    if (song) {
      setCurrentSong(song);
      // Navigate to player screen
      router.push('/player');
    }
  };
  
  // Helper function to check if an object is a Firestore timestamp
  const isFirestoreTimestamp = (obj: any): obj is FirestoreTimestamp => {
    return obj && typeof obj === 'object' && 'toDate' in obj && typeof obj.toDate === 'function';
  };
  
  // Format the week date range (Monday to Sunday)
  const formatWeekDate = (startDate: string | Date | FirestoreTimestamp) => {
    if (!startDate) return '';
    
    try {
      // Handle different date formats
      let start: Date;
      
      if (typeof startDate === 'string') {
        start = new Date(startDate);
      } else if (isFirestoreTimestamp(startDate)) {
        // Handle Firestore Timestamp
        start = startDate.toDate();
      } else if (startDate instanceof Date) {
        start = startDate;
      } else {
        // Unknown format
        return '';
      }
      
      // Check if date is valid
      if (isNaN(start.getTime())) {
        return '';
      }
      
      // Ensure we're using Monday as the start of the week
      const dayOfWeek = start.getUTCDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
      
      // If startDate is not Monday, adjust to the Monday of this week
      let mondayDate = new Date(start);
      if (dayOfWeek !== 1) {
        // Calculate days to adjust to get to Monday
        const daysToAdjust = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
        mondayDate.setUTCDate(mondayDate.getUTCDate() - daysToAdjust);
      }
      
      // Calculate end date (Sunday) by adding 6 days to Monday
      const end = new Date(mondayDate);
      end.setUTCDate(end.getUTCDate() + 6); // Monday + 6 days = Sunday
      
      // Format the date range
      const startDay = mondayDate.getUTCDate();
      const endDay = end.getUTCDate();
      const startMonth = mondayDate.toLocaleString('default', { month: 'short' });
      const endMonth = end.toLocaleString('default', { month: 'short' });
      
      // If start and end months are different, include both month names
      if (startMonth !== endMonth) {
        return `${startDay} ${startMonth} - ${endDay} ${endMonth}`;
      }
      
      // If same month, just show the date range
      return `${startDay}-${endDay} ${startMonth}`;
    } catch (error) {
      console.error('Error formatting week date:', error);
      return '';
    }
  };
  
  if (!selectedWeek) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.emptyText}>Week not found</Text>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>Go Back</Text>
        </Pressable>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: `Week ${selectedWeek.weekNumber}`,
          headerShown: true,
          headerStyle: {
            backgroundColor: Colors.dark.background,
          },
          headerTintColor: Colors.dark.text,
          headerLeft: () => (
            <Pressable onPress={() => router.back()} style={styles.headerBackButton}>
              <ArrowLeft size={24} color={Colors.dark.primary} />
            </Pressable>
          ),
        }} 
      />
      
      <View style={styles.headerRow}>
        <Calendar size={18} color={Colors.dark.primary} />
        <Text style={styles.headerText}>
          Week {selectedWeek.weekNumber}
          {selectedWeek.startDate ? ` (${formatWeekDate(selectedWeek.startDate)})` : ''}
        </Text>
      </View>
      
      {isLoadingSongs ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={Colors.dark.primary} />
          <Text style={styles.loadingText}>Loading hymns...</Text>
        </View>
      ) : (
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {selectedWeek.songs.map((songNumber, index) => {
            const song = weekSongs[index];
            const dayName = formatDayOfWeek(index);
            
            return (
              <Pressable 
                key={index}
                onPress={() => handleSongPress(song)}
                disabled={!song}
                style={styles.songCard}
              >
                <View style={styles.dayContainer}>
                  <Text style={styles.dayName}>{dayName}</Text>
                </View>
                
                <View style={styles.songDetails}>
                  <View style={styles.numberCircle}>
                    <Text style={styles.numberText}>{songNumber}</Text>
                  </View>
                  
                  <View style={styles.songInfo}>
                    {song ? (
                      <>
                        <Text style={styles.songTitle} numberOfLines={1}>{song.title}</Text>
                        <Text style={styles.songSubtitle} numberOfLines={1}>
                          {song.artist || 'Unknown'}
                        </Text>
                      </>
                    ) : (
                      <Text style={styles.loadingTitle}>Loading...</Text>
                    )}
                  </View>
                </View>
              </Pressable>
            );
          })}
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
    paddingHorizontal: 16,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 16,
  },
  headerText: {
    color: Colors.dark.text,
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  songCard: {
    backgroundColor: Colors.dark.surface,
    borderRadius: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'rgba(248, 250, 252, 0.1)',
    overflow: 'hidden',
  },
  dayContainer: {
    backgroundColor: 'rgba(248, 250, 252, 0.05)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(248, 250, 252, 0.1)',
  },
  dayName: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'uppercase',
  },
  songDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  numberCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(248, 250, 252, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  numberText: {
    color: Colors.dark.text,
    fontSize: 16,
    fontWeight: 'bold',
  },
  songInfo: {
    flex: 1,
  },
  songTitle: {
    color: Colors.dark.text,
    fontSize: 16,
    fontWeight: '500',
  },
  songSubtitle: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    marginTop: 2,
  },
  loadingTitle: {
    color: Colors.dark.textTertiary,
    fontSize: 14,
    fontStyle: 'italic',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: Colors.dark.textSecondary,
    marginTop: 8,
    fontSize: 14,
  },
  emptyText: {
    color: Colors.dark.textSecondary,
    fontSize: 16,
    textAlign: 'center',
  },
  backButton: {
    marginTop: 16,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: Colors.dark.primary,
    borderRadius: 8,
  },
  backButtonText: {
    color: Colors.dark.background,
    fontSize: 14,
    fontWeight: '600',
  },
  headerBackButton: {
    marginRight: 16,
  },
});
