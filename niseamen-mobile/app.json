{"expo": {"name": "NiseAmen", "slug": "niseamen", "version": "1.0.0", "runtimeVersion": "1.0.0", "updates": {"url": "https://u.expo.dev/119e89a0-617c-433b-a449-23f52c309a8a", "enabled": false, "checkAutomatically": "ON_ERROR_RECOVERY", "fallbackToCacheTimeout": 0}, "orientation": "portrait", "icon": "./assets/ic_launcher/ios/<EMAIL>", "scheme": "niseamen", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.isaacgounton.niseamen", "icon": "./assets/ic_launcher/ios/<EMAIL>"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/ic_launcher/android/mipmap-xxxhdpi/ic_launcher_foreground.png", "backgroundColor": "#ffffff"}, "package": "com.isaacgounton.niseamen", "icon": "./assets/ic_launcher/android/playstore-icon.png"}, "web": {"bundler": "metro", "favicon": "./assets/ic_launcher/android/mipmap-mdpi/ic_launcher.png", "config": {"googleSignIn": {"expoClientId": "578866985713-k34t7hq8o03h8pcnmnd9921gqpt8pkbg.apps.googleusercontent.com"}}}, "plugins": ["expo-router"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "119e89a0-617c-433b-a449-23f52c309a8a"}}, "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}