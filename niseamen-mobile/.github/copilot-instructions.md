# NiseAmen Mobile App

## Overview
NiseAmen is a music player application focused on religious songs with multilingual lyrics support. The app provides a seamless music listening experience with integrated lyrics viewing capabilities.

## Core Features

### 1. Music Player
#### Audio Controls
- Play/Pause functionality
- Next/Previous track navigation
- Progress bar with seek functionality
- Time display (current time/duration)
- Continuous playback

#### Now Playing UI
- Animated circular album art (spins during playback)
- Song title and artist display
- Progress bar with drag functionality
- Large play/pause button with surrounding navigation controls

### 2. Song List & Search
#### Search Functionality
- Real-time search with debouncing (300ms)
- Search through both titles and lyrics
- Highlighted search results
- Loading indicator during search

#### Song List Display
- Scrollable list of available songs
- Highlighted currently playing song
- Display matched lyrics in search results
- Smooth animations on selection

### 3. Lyrics View
#### Multilingual Support
- Primary support for Goun language
- Language selector dropdown
- Dynamic loading of lyrics based on selected language

#### Lyrics Display
- Clean, readable text formatting
- Auto-scrolling lyrics view
- Proper handling of line breaks
- Loading states for lyrics fetching

### 4. User Profile
#### Authentication
- Google Sign-In integration
- Profile picture display
- User email and name display

#### User Features
- Last played song tracking
- Favorite genre display
- Lyrics management access
- Sign out functionality

## Technical Requirements

### Package Manager
- **Bun** is used as the package manager for this project, not npm

### Firebase Integration
- Authentication (Google Sign-In)
- Firestore for data storage
- Real-time updates

### Search Implementation
- Algolia integration for advanced search
- Search through titles and lyrics
- Result highlighting

### Data Models
```typescript
interface Song {
  id: string;
  title: string;
  artist: string;
  url: string;
  lyrics: {
    [language: string]: string;
  };
}

interface User {
  uid: string;
  displayName: string;
  email: string;
  photoURL: string;
  lastPlayedSong?: string;
  favoriteGenre?: string;
}
```

## UI/UX Requirements
- Dark theme with purple accent colors
- Semi-transparent overlays (white/5 to white/20)
- Smooth animations for state changes
- Responsive design for different screen sizes
- Custom scrollbar styling

## Design Guidelines

### Color Scheme
- Background: Dark/Transparent
- Primary: Purple (#A855F7)
- Secondary: Cyan (#06B6D4)
- Text: White with varying opacity
- Overlays: White with 5-20% opacity

### Typography
- Headings: Extra Bold (font-extrabold)
- Body: Light to Regular weight
- Song titles: Semi-bold
- Proper line height for lyrics display

## Components

### Custom Audio Player
- Circular progress bar
- Custom play/pause icons
- Touch-friendly controls

### Search Bar
- Floating search icon
- Loading spinner during search
- Clear button

### Language Selector
- Custom dropdown design
- Clear language indicators

### Profile Card
- Circular profile picture with ring
- Stats display
- Action buttons

## Performance Considerations
- Implement lazy loading for song lists
- Cache lyrics data locally
- Optimize image loading and caching
- Handle offline functionality
- Implement proper error boundaries

## Security Requirements
- Secure audio file access
- User authentication state management
- Protected routes for authenticated users
- Secure API calls