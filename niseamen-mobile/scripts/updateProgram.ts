import { initializeApp, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Firebase Admin SDK (more reliable for script usage)
try {
  let app;
  
  // Check if we have service account credentials
  if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
    console.log(`Using service account from: ${process.env.GOOGLE_APPLICATION_CREDENTIALS}`);
    app = initializeApp();
  } else if (process.env.FIREBASE_SERVICE_ACCOUNT) {
    // Try to use inline credentials if provided
    try {
      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT);
      app = initializeApp({
        credential: cert(serviceAccount)
      });
    } catch (e) {
      console.error('Error parsing service account JSON:', e);
      throw new Error('Invalid service account configuration');
    }
  } else {
    // Fallback to regular config
    app = initializeApp({
      projectId: process.env.FIREBASE_PROJECT_ID,
    });
    console.warn('Using application default credentials - this may not work in all environments');
  }
  
  const db = getFirestore(app);

  // Define interfaces for our program data
  interface WeekProgram {
    week: string;
    date: string;
    songs: number[];
  }

  interface MonthProgram {
    month: string;
    year: string;
    weeks: WeekProgram[];
  }

  /**
   * Parse the program text file into structured data
   */
  function parseProgram(fileContent: string): MonthProgram[] {
    const lines = fileContent.trim().split('\n');
    const programs: MonthProgram[] = [];
    
    let currentMonth: MonthProgram | null = null;

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine === '') continue;
      
      // Check if it's a month line (Month Year format)
      if (!trimmedLine.startsWith('Week')) {
        const [month, year] = trimmedLine.split(' ');
        currentMonth = {
          month,
          year,
          weeks: []
        };
        programs.push(currentMonth);
      } 
      // It's a week line
      else if (currentMonth) {
        // Parse week line like: Week 1 (12-01-2025): 15, 258, 101, 162, 48, 21, 202
        const weekMatch = trimmedLine.match(/Week (\d+) \((\d{2}-\d{2}-\d{4})\): ([\d, bis]+)/);
        
        if (weekMatch) {
          const [, weekNum, date, songsString] = weekMatch;
          
          // Parse the song numbers, handling "bis" cases
          const songsPart = songsString.split(',').map(s => s.trim());
          const songs = songsPart.map(song => {
            // Handle cases like "46bis"
            if (song.includes('bis')) {
              return parseInt(song.replace('bis', ''));
            }
            return parseInt(song);
          });
          
          currentMonth.weeks.push({
            week: `Week ${weekNum}`,
            date,
            songs
          });
        }
      }
    }
    
    return programs;
  }

  /**
   * Validate the program data before uploading
   */
  function validateProgram(program: MonthProgram): boolean {
    if (!program.month || typeof program.month !== 'string') {
      console.error('Invalid month:', program.month);
      return false;
    }
    
    if (!program.year || typeof program.year !== 'string') {
      console.error('Invalid year:', program.year);
      return false;
    }
    
    if (!Array.isArray(program.weeks)) {
      console.error('Weeks is not an array');
      return false;
    }
    
    for (const week of program.weeks) {
      if (!week.week || typeof week.week !== 'string') {
        console.error('Invalid week number:', week.week);
        return false;
      }
      
      if (!week.date || typeof week.date !== 'string') {
        console.error('Invalid date:', week.date);
        return false;
      }
      
      if (!Array.isArray(week.songs)) {
        console.error('Songs is not an array for week:', week.week);
        return false;
      }
      
      for (const song of week.songs) {
        if (typeof song !== 'number' || isNaN(song)) {
          console.error('Invalid song number:', song, 'in week:', week.week);
          return false;
        }
      }
    }
    
    return true;
  }

  /**
   * Main function to update the database
   */
  async function updateCantiqueProgramInDatabase() {
    try {
      // 1. Read the programs.txt file
      console.log('Reading programs.txt file...');
      const filePath = path.resolve(__dirname, '../programs.txt');
      
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }
      
      const fileContent = fs.readFileSync(filePath, 'utf8');
      
      // 2. Parse the program data
      console.log('Parsing program data...');
      const programData = parseProgram(fileContent);
      console.log(`Found ${programData.length} months of programs`);
      
      if (programData.length === 0) {
        throw new Error('No program data parsed from file');
      }
      
      // 3. Delete existing cantique_program data
      console.log('Deleting existing cantique_program data...');
      const programCollection = db.collection('cantique_program');
      const existingDocs = await programCollection.get();
      
      console.log(`Found ${existingDocs.size} existing documents to delete`);
      
      let deleteCount = 0;
      for (const document of existingDocs.docs) {
        try {
          console.log(`Deleting document: ${document.id}`);
          await document.ref.delete();
          deleteCount++;
        } catch (err) {
          console.error(`Error deleting document ${document.id}:`, err);
        }
      }
      
      console.log(`Successfully deleted ${deleteCount} existing program documents`);
      
      // 4. Upload the new program data
      console.log('Uploading new program data...');
      let uploadCount = 0;
      
      for (const monthProgram of programData) {
        // Validate before upload
        if (!validateProgram(monthProgram)) {
          console.error('Skipping invalid program:', monthProgram);
          continue;
        }
        
        const docId = `${monthProgram.month.toLowerCase()}_${monthProgram.year}`;
        try {
          console.log(`Uploading ${docId}...`);
          await programCollection.doc(docId).set(monthProgram);
          uploadCount++;
          console.log(`Successfully uploaded ${docId}`);
        } catch (err) {
          console.error(`Error uploading ${docId}:`, err);
        }
      }
      
      console.log(`Successfully uploaded ${uploadCount} out of ${programData.length} program documents`);
      
      if (uploadCount === programData.length) {
        console.log('Program update completed successfully!');
      } else {
        console.warn(`Program update partially completed: ${uploadCount}/${programData.length} documents uploaded.`);
      }
      
    } catch (error) {
      console.error('Error updating program data:', error);
      process.exit(1);
    }
  }

  // Execute the update
  console.log('Starting cantique program update...');
  updateCantiqueProgramInDatabase()
    .then(() => {
      console.log('Database update completed.');
      process.exit(0);
    })
    .catch(err => {
      console.error('Unhandled error during database update:', err);
      process.exit(1);
    });

} catch (initError) {
  console.error('Failed to initialize Firebase:', initError);
  process.exit(1);
}
