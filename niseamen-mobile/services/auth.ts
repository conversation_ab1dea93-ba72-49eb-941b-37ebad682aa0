import { 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword, 
  signOut as firebaseSignOut,
  updateProfile,
  sendPasswordResetEmail,
  onAuthStateChanged,
  GoogleAuthProvider,
  signInWithCredential
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';
import { auth, firestore } from '@/config/firebase';
import { User } from '@/types/song';
import { Platform } from 'react-native';
import * as WebBrowser from 'expo-web-browser';
import * as Google from 'expo-auth-session/providers/google';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Register a new user
export const registerUser = async (
  email: string, 
  password: string, 
  displayName: string
): Promise<User> => {
  try {
    // Create user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const firebaseUser = userCredential.user;
    
    // Update profile with display name
    await updateProfile(firebaseUser, { displayName });
    
    // Create user document in Firestore
    const newUser: User = {
      id: firebaseUser.uid,
      displayName: displayName,
      email: email,
      photoUrl: firebaseUser.photoURL || 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=2340&auto=format&fit=crop',
    };
    
    await setDoc(doc(firestore, 'users', firebaseUser.uid), newUser);
    
    return newUser;
  } catch (error: any) {
    console.error('Error registering user:', error);
    throw new Error(error.message);
  }
};

// Sign in existing user
export const signIn = async (email: string, password: string): Promise<User> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const firebaseUser = userCredential.user;
    
    // Get user data from Firestore
    const userDoc = await getDoc(doc(firestore, 'users', firebaseUser.uid));
    
    if (userDoc.exists()) {
      return userDoc.data() as User;
    } else {
      // If user document doesn't exist, create one
      const newUser: User = {
        id: firebaseUser.uid,
        displayName: firebaseUser.displayName || 'User',
        email: firebaseUser.email || email,
        photoUrl: firebaseUser.photoURL || 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=2340&auto=format&fit=crop',
      };
      
      await setDoc(doc(firestore, 'users', firebaseUser.uid), newUser);
      return newUser;
    }
  } catch (error: any) {
    console.error('Error signing in:', error);
    throw new Error(error.message);
  }
};

// Google OAuth configuration
const GOOGLE_WEB_CLIENT_ID = '************-k34t7hq8o03h8pcnmnd9921gqpt8pkbg.apps.googleusercontent.com';
const GOOGLE_ANDROID_CLIENT_ID = '************-seuine61mghkfoo6jf70me29is82pci3.apps.googleusercontent.com';
const GOOGLE_IOS_CLIENT_ID = '************-26e067ueo82kn7bn7onv9vbdcuf8jbnv.apps.googleusercontent.com';

// Initialize Google Auth
export const useGoogleAuth = () => {
  return Google.useAuthRequest({
    expoClientId: GOOGLE_WEB_CLIENT_ID,
    iosClientId: GOOGLE_IOS_CLIENT_ID,
    androidClientId: GOOGLE_ANDROID_CLIENT_ID,
    webClientId: GOOGLE_WEB_CLIENT_ID,
  });
};

// Sign in with Google
export const signInWithGoogle = async (accessToken: string): Promise<User | null> => {
  try {
    // Create a Google credential with the token
    const credential = GoogleAuthProvider.credential(null, accessToken);
    
    // Sign in with credential
    const userCredential = await signInWithCredential(auth, credential);
    const firebaseUser = userCredential.user;
    
    // Check if user exists in Firestore
    const userDoc = await getDoc(doc(firestore, 'users', firebaseUser.uid));
    
    let userData: User;
    
    if (userDoc.exists()) {
      // Update existing user data
      userData = userDoc.data() as User;
      
      // Update photo URL if it has changed
      if (firebaseUser.photoURL && firebaseUser.photoURL !== userData.photoUrl) {
        userData.photoUrl = firebaseUser.photoURL;
        await updateDoc(doc(firestore, 'users', firebaseUser.uid), { photoUrl: firebaseUser.photoURL });
      }
    } else {
      // Create new user document
      userData = {
        id: firebaseUser.uid,
        displayName: firebaseUser.displayName || 'Google User',
        email: firebaseUser.email || '',
        photoUrl: firebaseUser.photoURL || 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=2340&auto=format&fit=crop',
      };
      
      await setDoc(doc(firestore, 'users', firebaseUser.uid), userData);
    }
    
    return userData;
  } catch (error: any) {
    console.error('Error signing in with Google:', error);
    throw new Error(error.message);
  }
};

// Persist Google auth state
export const persistGoogleAuthState = async (accessToken: string) => {
  try {
    await AsyncStorage.setItem('google_access_token', accessToken);
  } catch (error) {
    console.error('Error persisting Google auth state:', error);
  }
};

// Get persisted Google auth token
export const getPersistedGoogleAuthToken = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem('google_access_token');
  } catch (error) {
    console.error('Error getting persisted Google auth token:', error);
    return null;
  }
};

// Clear persisted Google auth token
export const clearPersistedGoogleAuthToken = async () => {
  try {
    await AsyncStorage.removeItem('google_access_token');
  } catch (error) {
    console.error('Error clearing persisted Google auth token:', error);
  }
};

// Sign out
export const signOut = async (): Promise<void> => {
  try {
    await firebaseSignOut(auth);
    await clearPersistedGoogleAuthToken();
  } catch (error: any) {
    console.error('Error signing out:', error);
    throw new Error(error.message);
  }
};

// Reset password
export const resetPassword = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error: any) {
    console.error('Error resetting password:', error);
    throw new Error(error.message);
  }
};

// Get current user data from Firestore
export const getCurrentUser = async (): Promise<User | null> => {
  const firebaseUser = auth.currentUser;
  
  if (!firebaseUser) return null;
  
  try {
    const userDoc = await getDoc(doc(firestore, 'users', firebaseUser.uid));
    
    if (userDoc.exists()) {
      return userDoc.data() as User;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

// Listen to auth state changes
export const subscribeToAuthChanges = (
  callback: (user: User | null) => void
) => {
  return onAuthStateChanged(auth, async (firebaseUser) => {
    if (firebaseUser) {
      try {
        const userDoc = await getDoc(doc(firestore, 'users', firebaseUser.uid));
        
        if (userDoc.exists()) {
          callback(userDoc.data() as User);
        } else {
          // Create user document if it doesn't exist
          const newUser: User = {
            id: firebaseUser.uid,
            displayName: firebaseUser.displayName || 'User',
            email: firebaseUser.email || '',
            photoUrl: firebaseUser.photoURL || 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=2340&auto=format&fit=crop',
          };
          
          await setDoc(doc(firestore, 'users', firebaseUser.uid), newUser);
          callback(newUser);
        }
      } catch (error) {
        console.error('Error in auth state change:', error);
        callback(null);
      }
    } else {
      callback(null);
    }
  });
};