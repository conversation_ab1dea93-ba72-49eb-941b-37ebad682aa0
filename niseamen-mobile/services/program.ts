import { collection, doc, getDoc, getDocs, query, where, orderBy, limit } from 'firebase/firestore';
import { firestore } from '@/config/firebase';
import { MonthProgram, WeekProgram, FirestoreTimestamp } from '@/types/program';
import AsyncStorage from '@react-native-async-storage/async-storage';

const PROGRAM_CACHE_KEY = 'program_cache';
const FEATURED_SONG_CACHE_KEY = 'featured_song_cache';
const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 hours

// Helper function to check if an object is a Firestore timestamp
const isFirestoreTimestamp = (obj: any): obj is FirestoreTimestamp => {
  return obj && typeof obj === 'object' && 'toDate' in obj && typeof obj.toDate === 'function';
};

// Get the current date information in GMT+1 timezone
export const getCurrentDateInfo = () => {
  // Get current date in UTC
  const now = new Date();
  
  // Convert to GMT+1 by adding one hour (use the Date object's UTC methods to avoid DST issues)
  const gmtPlus1Time = new Date(now.getTime());
  gmtPlus1Time.setUTCHours(gmtPlus1Time.getUTCHours() + 1);
  
  // Extract date components in GMT+1
  const year = gmtPlus1Time.getUTCFullYear();
  const month = gmtPlus1Time.getUTCMonth() + 1; // JavaScript months are 0-indexed
  const date = gmtPlus1Time.getUTCDate();
  
  // Get day of week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
  const dayOfWeek = gmtPlus1Time.getUTCDay();
  
  // Get array index for the song (0 = Monday, 6 = Sunday)
  // Since dayOfWeek is 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  // We need to convert it to 0 = Monday, ..., 6 = Sunday
  const songIndex = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
  
  // Calculate ISO week number starting from Monday
  // Use a helper function to handle year boundaries correctly
  const getISOWeek = (dt: Date) => {
    // Copy date to avoid modifying original
    const d = new Date(Date.UTC(dt.getUTCFullYear(), dt.getUTCMonth(), dt.getUTCDate()));
    
    // Set to nearest Thursday: current date + 4 - current day number
    // Make Sunday's day number 7
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    
    // Get first day of year
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    
    // Calculate full weeks to nearest Thursday
    const weekNumber = Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
    
    return weekNumber;
  };
  
  const weekNumber = getISOWeek(gmtPlus1Time);
  
  return { 
    year, 
    month, 
    date,
    dayOfWeek: songIndex, // Index for songs array (0 = Monday, 6 = Sunday)
    weekNumber,
    // Add a dateString for caching purposes
    dateString: `${year}-${month}-${date}`
  };
};

// Fetch the current month's program
export const fetchCurrentMonthProgram = async (): Promise<MonthProgram | null> => {
  try {
    const { year, month } = getCurrentDateInfo();
    
    // Try to get from cache first
    const cachedData = await AsyncStorage.getItem(PROGRAM_CACHE_KEY);
    if (cachedData) {
      const { program, timestamp } = JSON.parse(cachedData);
      // Check if cache is still valid and matches current month/year
      if (Date.now() - timestamp < CACHE_EXPIRATION && 
          program && program.year === year && program.month === month) {
        console.log('Using cached program data');
        return program;
      }
    }
    
    // If no valid cache, fetch from Firestore
    console.log(`Fetching program for ${year}-${month} from Firestore`);
    
    // Convert month number to month name
    const months = ['january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december'];
    const monthName = months[month - 1].toLowerCase();
    
    // Get the program using the correct document ID format (e.g., "march2025")
    // Document ID format in Firebase is "month_year" (e.g., "march_2025")
    const docId = `${monthName}_${year}`;
    console.log('Fetching program with document ID:', docId);
    console.log('Collection name:', 'cantique_program');
    const programDoc = await getDoc(doc(firestore, 'cantique_program', docId));
    
    if (programDoc.exists()) {
      const rawData = programDoc.data() as { month: string; weeks: { date: string; songs: number[]; }[] };
      console.log('Raw data from Firebase:', JSON.stringify(rawData, null, 2));
      console.log('Number of weeks:', rawData.weeks?.length || 0);

      // Convert the raw data format to our MonthProgram format
      const program: MonthProgram = {
        year,
        month,
        weeks: rawData.weeks.map((week, index) => {
          // Parse the date string from "DD-MM-YYYY" format - this is the Sunday mass date
          const [day, monthNum, yearNum] = week.date.split('-').map(Number);
          const massSunday = new Date(Date.UTC(yearNum, monthNum - 1, day));
          massSunday.setUTCHours(1); // Set to GMT+1
          
          // Get Monday by subtracting 6 days from Sunday
          const weekStartDate = new Date(massSunday);
          weekStartDate.setUTCDate(weekStartDate.getUTCDate() - 6);
          weekStartDate.setUTCHours(1); // Ensure GMT+1
          
          return {
            weekNumber: index + 1, // Week numbers are 1-based
            startDate: weekStartDate.toISOString(), // Store Monday as start date
            songs: week.songs // Songs array is already in the correct format
          };
        })
      };
      
      // Cache the program
      await AsyncStorage.setItem(PROGRAM_CACHE_KEY, JSON.stringify({
        program,
        timestamp: Date.now()
      }));
      
      return program;
    }
    
    // If exact month not found, generate fallback
    console.log('No programs found, generating fallback');
    return generateFallbackProgram(year, month);
  } catch (error) {
    console.error('Error fetching month program:', error);
    const { year, month } = getCurrentDateInfo();
    return generateFallbackProgram(year, month);
  }
};

// Get the current week's program
export const getCurrentWeekProgram = async (): Promise<WeekProgram | null> => {
  try {
    const { weekNumber, year, month, date } = getCurrentDateInfo();
    const monthProgram = await fetchCurrentMonthProgram();
    
    if (!monthProgram || !monthProgram.weeks || monthProgram.weeks.length === 0) {
      console.log('No month program available, generating fallback week');
      return generateFallbackWeek(weekNumber);
    }
    
    // Create a date object for today in GMT+1
    const today = new Date();
    today.setUTCHours(today.getUTCHours() + 1);
    
    // Find the week that contains today's date
    let currentWeek = null;
    
    // First try to find by date range (most accurate)
    for (const week of monthProgram.weeks) {
      if (week.startDate) {
        let weekStartDate = null;
        
        try {
          if (typeof week.startDate === 'string') {
            weekStartDate = new Date(week.startDate);
          } else if (isFirestoreTimestamp(week.startDate)) {
            weekStartDate = week.startDate.toDate();
          } else if (week.startDate instanceof Date) {
            weekStartDate = week.startDate;
          }
        } catch (error) {
          console.error('Error parsing week start date:', error);
          continue; // Skip if we can't get a valid date
        }
        
        if (!weekStartDate) continue;
        
        // weekStartDate is Monday, weekEndDate is Sunday (mass day)
        const weekStartDateGMT1 = new Date(weekStartDate);
        weekStartDateGMT1.setUTCHours(1); // Set to GMT+1
        
        const weekEndDateGMT1 = new Date(weekStartDateGMT1);
        weekEndDateGMT1.setUTCDate(weekEndDateGMT1.getUTCDate() + 6); // Add 6 days to get to Sunday
        weekEndDateGMT1.setUTCHours(23); // End of day in GMT+1
        
        if (today >= weekStartDateGMT1 && today <= weekEndDateGMT1) {
          currentWeek = week;
          console.log(`Found week by date range: Week ${week.weekNumber}`);
          break;
        }
      }
    }
    
    if (!currentWeek) {
      console.log('No matching week found in month program, using fallback');
      return generateFallbackWeek(weekNumber);
    }
    
    return currentWeek;
  } catch (error) {
    console.error('Error getting current week program:', error);
    const { weekNumber } = getCurrentDateInfo();
    return generateFallbackWeek(weekNumber);
  }
};

// Get today's featured song with proper caching to ensure it only changes once per day
export const getTodaysFeaturedSong = async (): Promise<number | null> => {
  try {
    const dateInfo = getCurrentDateInfo();
    const { dayOfWeek, dateString } = dateInfo;
    
    // Check if we already have a cached featured song for today
    const cachedData = await AsyncStorage.getItem(FEATURED_SONG_CACHE_KEY);
    if (cachedData) {
      const { songNumber, date } = JSON.parse(cachedData);
      
      // If the cached song is for today, return it
      if (date === dateString) {
        console.log(`Using cached featured song ${songNumber} for ${dateString}`);
        return songNumber;
      }
    }
    
    // If no valid cache, get the current week's program
    const currentWeek = await getCurrentWeekProgram();
    
    if (!currentWeek || !currentWeek.songs || currentWeek.songs.length < 7) {
      console.log('No valid week program found for featured song, using fallback');
      const fallbackSong = getFallbackFeaturedSong(dayOfWeek);
      
      // Cache the fallback song for today
      await AsyncStorage.setItem(FEATURED_SONG_CACHE_KEY, JSON.stringify({
        songNumber: fallbackSong,
        date: dateString
      }));
      
      return fallbackSong;
    }
    
    // Get the song for the current day of the week (Monday-based)
    const featuredSong = currentWeek.songs[dayOfWeek];
    console.log(`Setting featured song ${featuredSong} for day ${dayOfWeek} (${dateString})`);
    
    // Cache the featured song for today
    await AsyncStorage.setItem(FEATURED_SONG_CACHE_KEY, JSON.stringify({
      songNumber: featuredSong,
      date: dateString
    }));
    
    return featuredSong;
  } catch (error) {
    console.error('Error getting today\'s featured song:', error);
    const { dayOfWeek, dateString } = getCurrentDateInfo();
    const fallbackSong = getFallbackFeaturedSong(dayOfWeek);
    
    // Cache the fallback song for today
    try {
      await AsyncStorage.setItem(FEATURED_SONG_CACHE_KEY, JSON.stringify({
        songNumber: fallbackSong,
        date: dateString
      }));
    } catch (cacheError) {
      console.error('Error caching featured song:', cacheError);
    }
    
    return fallbackSong;
  }
};

// Generate a fallback month program if none exists in Firestore
const generateFallbackProgram = (year: number, month: number): MonthProgram => {
  const weeks: WeekProgram[] = [];
  
  // Get the first day of the month in GMT+1
  const firstDayOfMonth = new Date(Date.UTC(year, month - 1, 1));
  firstDayOfMonth.setUTCHours(1); // Set to GMT+1
  
  // Calculate the Monday of the week containing the first day of the month
  const dayOfWeek = firstDayOfMonth.getUTCDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  const daysToAdjust = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
  const firstMonday = new Date(firstDayOfMonth);
  firstMonday.setUTCDate(firstMonday.getUTCDate() - daysToAdjust);
  
  // Generate 4-5 weeks for the month
  for (let i = 0; i < 5; i++) {
    const weekStartDate = new Date(firstMonday);
    weekStartDate.setUTCDate(weekStartDate.getUTCDate() + (i * 7));
    
    // Skip if this week starts in the next month
    if (weekStartDate.getUTCMonth() + 1 > month && weekStartDate.getUTCDate() > 7) {
      continue;
    }
    
    // Calculate ISO week number
    const weekDate = new Date(weekStartDate);
    weekDate.setUTCDate(weekDate.getUTCDate() + 3); // Thursday of the week
    const yearStart = new Date(Date.UTC(weekDate.getUTCFullYear(), 0, 1));
    const weekNumber = Math.ceil((((weekDate.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
    
    // Generate 7 song numbers for each week
    const weekSeed = weekStartDate.getTime();
    const songs = Array.from({ length: 7 }, (_, index) => {
      const hash = (weekSeed + index * 24 * 60 * 60 * 1000) % 300;
      return Math.max(1, Math.floor(hash) + 1);
    });
    
    weeks.push({
      weekNumber,
      startDate: weekStartDate.toISOString(),
      songs
    });
  }
  
  return {
    year,
    month,
    weeks
  };
};

// Generate a fallback week if none exists
const generateFallbackWeek = (weekNumber: number): WeekProgram => {
  const { year, month, date } = getCurrentDateInfo();
  
  // Get the current date in GMT+1
  const today = new Date();
  today.setUTCHours(today.getUTCHours() + 1);
  
  // Calculate the Monday of the current week
  const dayOfWeek = today.getUTCDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  const daysToAdjust = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
  const monday = new Date(today);
  monday.setUTCDate(monday.getUTCDate() - daysToAdjust);
  
  // Generate 7 song numbers
  const weekSeed = monday.getTime();
  const songs = Array.from({ length: 7 }, (_, index) => {
    const hash = (weekSeed + index * 24 * 60 * 60 * 1000) % 300;
    return Math.max(1, Math.floor(hash) + 1);
  });
  
  return {
    weekNumber,
    startDate: monday.toISOString(),
    songs
  };
};

// Get a fallback featured song based on day of week
const getFallbackFeaturedSong = (dayOfWeek: number): number => {
  const { year, month, date } = getCurrentDateInfo();
  
  // Create a date for today in GMT+1
  const today = new Date();
  today.setUTCHours(today.getUTCHours() + 1);
  
  // Generate a deterministic song number based on the date
  const daySeed = today.getTime();
  const hash = (daySeed + dayOfWeek * 24 * 60 * 60 * 1000) % 300;
  return Math.max(1, Math.floor(hash) + 1);
};

// Format date to display month and year
export const formatMonthYear = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
};

// Format date to display day of week
export const formatDayOfWeek = (dayIndex: number): string => {
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  return days[dayIndex];
};
