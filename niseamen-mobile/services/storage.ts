import { ref, getDownloadURL } from 'firebase/storage';
import { storage } from '@/config/firebase';

// Cache for image URLs to avoid repeated requests
const imageUrlCache: Record<string, { url: string, timestamp: number }> = {};
const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 hours

// Default album art URL - direct link that doesn't require Firebase auth
const DEFAULT_ALBUM_ART = 'https://filebrowser.etugrand.com/api/public/dl/3Brw2rx4/home/<USER>/CantiqueECC.webp';

/**
 * Get a download URL for a file in Firebase Storage
 * @param path The path to the file in storage
 * @returns The download URL
 */
export const getStorageUrl = async (path: string): Promise<string> => {
  try {
    // Check if we have a cached URL that's still valid
    if (imageUrlCache[path] && (Date.now() - imageUrlCache[path].timestamp < CACHE_EXPIRATION)) {
      return imageUrlCache[path].url;
    }
    
    // Get the download URL from Firebase Storage
    const fileRef = ref(storage, path);
    const url = await getDownloadURL(fileRef);
    
    // Cache the URL
    imageUrlCache[path] = {
      url,
      timestamp: Date.now()
    };
    
    return url;
  } catch (error) {
    console.error(`Error getting download URL for ${path}:`, error);
    throw error;
  }
};

/**
 * Get the album art URL for a song
 * @returns The default album art URL
 */
export const getDefaultAlbumArt = async (): Promise<string> => {
  // Simply return the direct URL instead of trying to fetch from Firebase
  // This avoids the Firebase Storage permission issues
  return DEFAULT_ALBUM_ART;
};