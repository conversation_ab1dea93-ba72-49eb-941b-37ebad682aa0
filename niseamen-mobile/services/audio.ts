import { Audio } from 'expo-av';
import { Platform } from 'react-native';
import { Song } from '@/types/song';

// Keep track of the sound object
let sound: Audio.Sound | null = null;

// Initialize audio
export const initAudio = async (): Promise<void> => {
  try {
    await Audio.setAudioModeAsync({
      playsInSilentModeIOS: true,
      staysActiveInBackground: true,
      shouldDuckAndroid: true,
    });
  } catch (error) {
    console.error('Failed to initialize audio:', error);
  }
};

// Load and play a song
export const playSong = async (
  song: Song,
  onPlaybackStatusUpdate: (status: { 
    isLoaded: boolean;
    positionMillis?: number;
    durationMillis?: number;
    isPlaying?: boolean;
    didJustFinish?: boolean;
  }) => void
): Promise<void> => {
  try {
    // Unload any existing sound
    if (sound) {
      await sound.unloadAsync();
      sound = null;
    }
    
    // Create a new sound object
    const { sound: newSound, status } = await Audio.Sound.createAsync(
      { uri: song.audioUrl },
      { shouldPlay: true },
      onPlaybackStatusUpdate
    );
    
    sound = newSound;
  } catch (error) {
    console.error('Failed to play song:', error);
    throw new Error('Failed to play song. Please try again.');
  }
};

// Play/pause the current song
export const togglePlayback = async (): Promise<boolean> => {
  if (!sound) return false;
  
  try {
    const status = await sound.getStatusAsync();
    
    if (status.isLoaded) {
      if (status.isPlaying) {
        await sound.pauseAsync();
        return false; // Paused
      } else {
        await sound.playAsync();
        return true; // Playing
      }
    }
    
    // If status is not loaded or doesn't have isPlaying property, return false
    return false;
  } catch (error) {
    console.error('Failed to toggle playback:', error);
    return false;
  }
};

// Seek to a specific position
export const seekTo = async (seconds: number): Promise<void> => {
  if (!sound) return;
  
  try {
    await sound.setPositionAsync(seconds * 1000);
  } catch (error) {
    console.error('Failed to seek:', error);
  }
};

// Clean up resources
export const unloadSound = async (): Promise<void> => {
  if (!sound) return;
  
  try {
    await sound.unloadAsync();
    sound = null;
  } catch (error) {
    console.error('Failed to unload sound:', error);
  }
};
