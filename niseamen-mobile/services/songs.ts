import { collection, getDocs, doc, getDoc, query, orderBy, limit } from 'firebase/firestore';
import { ref, getDownloadURL } from 'firebase/storage';
import { firestore, storage } from '@/config/firebase';
import { Song, Language } from '@/types/song';
import AsyncStorage from '@react-native-async-storage/async-storage';

const SONGS_CACHE_KEY = 'songs_cache';
const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// Default album art URL as fallback
const DEFAULT_ALBUM_ART = 'https://filebrowser.etugrand.com/api/public/dl/3Brw2rx4/home/<USER>/CantiqueECC.webp';

// Fetch all songs from Firestore with caching and optimization
export const fetchSongs = async (): Promise<Song[]> => {
  try {
    // Try to get songs from cache first
    const cachedData = await AsyncStorage.getItem(SONGS_CACHE_KEY);
    if (cachedData) {
      const { songs, timestamp } = JSON.parse(cachedData);
      // Check if cache is still valid (less than 24 hours old)
      if (Date.now() - timestamp < CACHE_EXPIRATION && songs.length > 0) {
        console.log('Using cached songs data');
        return songs;
      }
    }
    
    // If no valid cache, fetch from Firestore
    console.log('Fetching songs from Firestore');
    
    // Get all songs, ordered by ID
    const songsCollection = collection(firestore, 'songs');
    const songsQuery = query(songsCollection, orderBy('id'));
    const songsSnapshot = await getDocs(songsQuery);
    
    if (songsSnapshot.empty) {
      console.log('No songs found in Firestore');
      return [];
    }
    
    const songs: Song[] = [];
    const processingPromises: Promise<void>[] = [];
    
    // Process all songs in parallel
    for (const songDoc of songsSnapshot.docs) {
      const processPromise = processSongDocument(songDoc, DEFAULT_ALBUM_ART)
        .then(song => {
          if (song) songs.push(song);
        })
        .catch(error => {
          console.error(`Error processing song ${songDoc.id}:`, error);
        });
      
      processingPromises.push(processPromise);
    }
    
    // Wait for all songs to be processed
    await Promise.all(processingPromises);
    
    // Sort songs by ID
    songs.sort((a, b) => {
      const aNum = parseInt(a.id, 10);
      const bNum = parseInt(b.id, 10);
      return aNum - bNum;
    });
    
    // Cache the songs
    if (songs.length > 0) {
      await AsyncStorage.setItem(SONGS_CACHE_KEY, JSON.stringify({
        songs: songs,
        timestamp: Date.now()
      }));
    }
    
    return songs;
  } catch (error) {
    console.error('Error fetching songs:', error);
    
    // Try to get songs from cache as fallback, even if expired
    try {
      const cachedData = await AsyncStorage.getItem(SONGS_CACHE_KEY);
      if (cachedData) {
        const { songs } = JSON.parse(cachedData);
        if (songs && songs.length > 0) {
          console.log('Using expired cached songs data as fallback');
          return songs;
        }
      }
    } catch (cacheError) {
      console.error('Error reading cache:', cacheError);
    }
    
    // Return empty array as last resort
    return [];
  }
};

// Helper function to process a song document
const processSongDocument = async (songDoc: any, defaultAlbumArt: string): Promise<Song | null> => {
  try {
    const songData = songDoc.data();
    
    // Get the audio URL from Firebase Storage using the url field from the song document
    let audioUrl = '';
    try {
      if (songData.url) {
        // Use the url field from the song document
        const audioRef = ref(storage, songData.url);
        audioUrl = await getDownloadURL(audioRef);
      } else {
        // Fallback to a default path if url is not provided
        const fileName = `music/${songDoc.id.padStart(3, '0')} Cantique ECC.mp3`;
        const audioRef = ref(storage, fileName);
        audioUrl = await getDownloadURL(audioRef);
      }
    } catch (error) {
      console.error(`Error getting audio URL for song ${songDoc.id}:`, error);
      // Use a placeholder URL if we can't get the real one
      audioUrl = '';
    }
    
    // Always use the default album art for all songs
    const imageUrl = defaultAlbumArt;
    
    // Get lyrics for each language
    const lyrics: { [key in Language]?: string } = {};
    
    // Get Goun lyrics (default)
    try {
      const gounLyricsDoc = await getDoc(doc(firestore, 'lyrics', songDoc.id));
      if (gounLyricsDoc.exists()) {
        lyrics.goun = gounLyricsDoc.data().text;
      } else {
        lyrics.goun = "No lyrics available";
      }
    } catch (error) {
      console.error(`Error getting Goun lyrics for song ${songDoc.id}:`, error);
      lyrics.goun = "No lyrics available";
    }
    
    // Get French lyrics
    try {
      const frenchLyricsDoc = await getDoc(doc(firestore, 'lyrics_francais', songDoc.id));
      if (frenchLyricsDoc.exists()) {
        lyrics.french = frenchLyricsDoc.data().text;
      } else {
        lyrics.french = "Pas de paroles disponibles";
      }
    } catch (error) {
      console.error(`Error getting French lyrics for song ${songDoc.id}:`, error);
      lyrics.french = "Pas de paroles disponibles";
    }
    
    return {
      id: songDoc.id,
      title: songData.title || `Cantique ${songDoc.id}`,
      artist: songData.artist || 'Église du christianisme céleste',
      imageUrl,
      audioUrl,
      duration: songData.duration,
      songNumber: parseInt(songDoc.id, 10),
      lyrics: lyrics as { [key in Language]: string },
    };
  } catch (error) {
    console.error(`Error processing song document ${songDoc.id}:`, error);
    return null;
  }
};

// Fetch a single song by ID with caching
export const fetchSongById = async (songId: string): Promise<Song | null> => {
  try {
    // Try to get from cache first
    const cachedData = await AsyncStorage.getItem(SONGS_CACHE_KEY);
    if (cachedData) {
      const { songs } = JSON.parse(cachedData);
      const cachedSong = songs.find((s: Song) => s.id === songId);
      if (cachedSong) {
        console.log('Using cached song data');
        return cachedSong;
      }
    }
    
    const songDoc = await getDoc(doc(firestore, 'songs', songId));
    
    if (!songDoc.exists()) {
      return null;
    }
    
    // Use direct URL instead of trying to fetch from Firebase
    const song = await processSongDocument(songDoc, DEFAULT_ALBUM_ART);
    return song;
  } catch (error) {
    console.error('Error fetching song by ID:', error);
    return null;
  }
};
