{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["lucide-react-native"], "listUnknownPackages": false}}}, "scripts": {"start": "expo start --tunnel", "start-web": "expo start --web --tunnel", "start-web-dev": "DEBUG=expo* expo start --web --tunnel", "update-program": "bun run scripts/updateProgram.ts", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-firebase/app": "^21.12.0", "@react-native-firebase/auth": "^21.12.0", "@react-navigation/native": "^7.0.0", "expo": "~52.0.36", "expo-auth-session": "~6.0.3", "expo-av": "~15.0.2", "expo-blur": "~14.0.1", "expo-constants": "~17.0.7", "expo-font": "~13.0.4", "expo-haptics": "~14.0.0", "expo-image": "~2.0.6", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "^14.0.1", "expo-linking": "~7.0.3", "expo-location": "~18.0.7", "expo-router": "~4.0.17", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.0", "expo-symbols": "~0.2.0", "expo-system-ui": "~4.0.6", "expo-updates": "^0.27.2", "expo-web-browser": "~14.0.2", "firebase": "^11.4.0", "lucide-react-native": "^0.475.0", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.7", "react-native-gesture-handler": "~2.20.2", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.13", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@types/react": "~18.3.12", "dotenv": "^16.4.7", "firebase-admin": "^13.2.0", "typescript": "~5.3.3"}, "private": true}