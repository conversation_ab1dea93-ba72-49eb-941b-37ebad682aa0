import React from 'react';
import { View, Text, StyleSheet, Pressable, ScrollView } from 'react-native';
import { Song } from '@/types/song';
import { usePlayerStore } from '@/stores/player-store';
import { useProgramStore } from '@/stores/program-store';
import { formatMonthYear, getCurrentDateInfo } from '@/services/program';
import Colors from '@/constants/colors';
import { Calendar, ChevronRight } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { FirestoreTimestamp } from '@/types/program';

export default function MonthlyProgram() {
  const { currentMonth, currentWeek } = useProgramStore();
  const { queue, setCurrentSong } = usePlayerStore();
  const router = useRouter();
  
  // Get today's date info from the shared utility function
  const { weekNumber: currentWeekNumber, dayOfWeek: todayIndex } = getCurrentDateInfo();
  
  if (!currentMonth || !currentMonth.weeks || currentMonth.weeks.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No monthly program available</Text>
      </View>
    );
  }
  
  // Find songs in queue by song number
  const findSongById = (songNumber: number): Song | undefined => {
    if (!Array.isArray(queue)) return undefined;
    return queue.find(song => song?.songNumber === songNumber);
  };
  
  // Format month and year
  const monthYearText = formatMonthYear(new Date(currentMonth.year, currentMonth.month - 1));
  
  // Helper function to check if an object is a Firestore timestamp
  const isFirestoreTimestamp = (obj: any): obj is FirestoreTimestamp => {
    return obj && typeof obj === 'object' && 'toDate' in obj && typeof obj.toDate === 'function';
  };
  
  // Format week date range (Monday to Sunday)
  const formatWeekDate = (startDate: string | Date | FirestoreTimestamp) => {
    if (!startDate) return '';
    
    try {
      // Handle different date formats
      let start: Date;
      
      if (typeof startDate === 'string') {
        start = new Date(startDate);
      } else if (isFirestoreTimestamp(startDate)) {
        // Handle Firestore Timestamp
        start = startDate.toDate();
      } else if (startDate instanceof Date) {
        start = startDate;
      } else {
        // Unknown format
        return '';
      }
      
      // Check if date is valid
      if (isNaN(start.getTime())) {
        return '';
      }
      
      // Ensure we're using Monday as the start of the week
      const dayOfWeek = start.getUTCDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
      
      // If startDate is not Monday, adjust to the Monday of this week
      let mondayDate = new Date(start);
      if (dayOfWeek !== 1) {
        // Calculate days to adjust to get to Monday
        const daysToAdjust = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
        mondayDate.setUTCDate(mondayDate.getUTCDate() - daysToAdjust);
      }
      
      // Calculate end date (Sunday) by adding 6 days to Monday
      const end = new Date(mondayDate);
      end.setUTCDate(end.getUTCDate() + 6); // Monday + 6 days = Sunday
      
      // Format the date range
      const startDay = mondayDate.getUTCDate();
      const endDay = end.getUTCDate();
      const startMonth = mondayDate.toLocaleString('default', { month: 'short' });
      const endMonth = end.toLocaleString('default', { month: 'short' });
      
      // If start and end months are different, include both month names
      if (startMonth !== endMonth) {
        return `${startDay} ${startMonth} - ${endDay} ${endMonth}`;
      }
      
      // If same month, just show the date range
      return `${startDay}-${endDay} ${startMonth}`;
    } catch (error) {
      console.error('Error formatting week date:', error);
      return '';
    }
  };
  
  // Handle song press with proper event handling
  const handleSongPress = (song: Song | undefined) => {
    if (song) {
      setCurrentSong(song);
      // Navigate to player screen
      router.push('/player');
    }
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <Calendar size={18} color={Colors.dark.primary} />
        <Text style={styles.headerText}>{monthYearText}</Text>
      </View>
      
      {currentMonth.weeks.map((week, weekIndex) => {
        // Mark the week as current if it matches the current week from the program store
        // or if its weekNumber matches the calculated current week number
        const isCurrentWeek = currentWeek ? 
          week.weekNumber === currentWeek.weekNumber : 
          week.weekNumber === currentWeekNumber;
        
        return (
          <LinearGradient
            key={weekIndex}
            colors={isCurrentWeek 
              ? ['rgba(226, 177, 60, 0.2)', 'rgba(30, 41, 59, 0.8)'] 
              : [Colors.dark.surface, Colors.dark.surface]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={[styles.weekCard, isCurrentWeek && styles.currentWeekCard]}
          >
            <View style={styles.weekHeader}>
              <Text style={[styles.weekTitle, isCurrentWeek && styles.currentWeekText]}>
                Week {week.weekNumber}
                {week.startDate ? ` (${formatWeekDate(week.startDate)})` : ''}
              </Text>
              {isCurrentWeek && (
                <View style={styles.currentBadge}>
                  <Text style={styles.currentBadgeText}>Current</Text>
                </View>
              )}
            </View>
            
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.daysScrollContent}
            >
              {(week.songs || []).map((songNumber, dayIndex) => {
                const song = findSongById(songNumber);
                const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
                // Highlight today's song if this is the current week
                const isToday = isCurrentWeek && todayIndex === dayIndex;
                
                return (
                  <Pressable 
                    key={dayIndex}
                    style={styles.daySongItem}
                    onPress={() => handleSongPress(song)}
                    disabled={!song}
                  >
                    <Text style={[
                      styles.dayLabel,
                      isToday && styles.currentDayLabel
                    ]}>{dayNames[dayIndex]}</Text>
                    <View style={[
                      styles.songNumberBadge,
                      isToday && styles.currentDaySongBadge
                    ]}>
                      <Text style={[
                        styles.songNumberText,
                        isToday && styles.currentDaySongText
                      ]}>{`${songNumber}`}</Text>
                    </View>
                  </Pressable>
                );
              })}
            </ScrollView>
            
            <Pressable 
              style={styles.viewAllButton}
              onPress={() => router.push({
                pathname: '/week-details',
                params: { weekNumber: week.weekNumber }
              })}
            >
              <Text style={styles.viewAllText}>View Details</Text>
              <ChevronRight size={16} color={Colors.dark.primary} />
            </Pressable>
          </LinearGradient>
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    color: Colors.dark.text,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  weekCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'rgba(248, 250, 252, 0.1)',
  },
  currentWeekCard: {
    borderColor: Colors.dark.primary,
  },
  weekHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  weekTitle: {
    color: Colors.dark.text,
    fontSize: 14,
    fontWeight: '600',
  },
  currentWeekText: {
    color: Colors.dark.primary,
  },
  currentBadge: {
    backgroundColor: Colors.dark.primary,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  currentBadgeText: {
    color: Colors.dark.background,
    fontSize: 10,
    fontWeight: 'bold',
  },
  daysScrollContent: {
    paddingBottom: 8,
  },
  daySongItem: {
    alignItems: 'center',
    marginRight: 16,
  },
  dayLabel: {
    color: Colors.dark.textSecondary,
    fontSize: 12,
    marginBottom: 4,
  },
  currentDayLabel: {
    color: Colors.dark.primary,
    fontWeight: '600',
  },
  songNumberBadge: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(248, 250, 252, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  currentDaySongBadge: {
    backgroundColor: Colors.dark.primary,
  },
  songNumberText: {
    color: Colors.dark.text,
    fontSize: 14,
    fontWeight: 'bold',
  },
  currentDaySongText: {
    color: Colors.dark.background,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(248, 250, 252, 0.1)',
  },
  viewAllText: {
    color: Colors.dark.primary,
    fontSize: 14,
    marginRight: 4,
  },
  emptyContainer: {
    backgroundColor: Colors.dark.surface,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyText: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    textAlign: 'center',
  },
});
