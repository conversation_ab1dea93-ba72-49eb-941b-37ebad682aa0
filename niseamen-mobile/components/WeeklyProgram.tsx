import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Pressable, ActivityIndicator, ScrollView } from 'react-native';
import { usePlayerStore } from '@/stores/player-store';
import { useProgramStore } from '@/stores/program-store';
import { formatDayOfWeek, getCurrentDateInfo } from '@/services/program';
import Colors from '@/constants/colors';
import { Calendar, Music } from 'lucide-react-native';
import { fetchSongById } from '@/services/songs';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Song } from '@/types/song';
import { FirestoreTimestamp } from '@/types/program';

export default function WeeklyProgram() {
  const { currentWeek, isLoading: programLoading } = useProgramStore();
  const { queue, setCurrentSong } = usePlayerStore();
  const [isLoadingSongs, setIsLoadingSongs] = useState(false);
  const [weekSongs, setWeekSongs] = useState<Array<Song | null>>([]);
  const router = useRouter();
  
  // Get the current day (Monday-based: 0 = Monday, 6 = Sunday)
  const todayIndex = getCurrentDateInfo().dayOfWeek;
  
  // Load songs for the week that might not be in the queue
  useEffect(() => {
    const loadWeekSongs = async () => {
      if (!currentWeek || !currentWeek.songs || currentWeek.songs.length === 0) {
        return;
      }
      
      setIsLoadingSongs(true);
      
      try {
        const songsToLoad: Array<Song | null> = [];
        
        // For each song number in the week
        for (let i = 0; i < currentWeek.songs.length; i++) {
          const songNumber = currentWeek.songs[i];
          
          // First check if it's in the queue
          const songInQueue = queue.find(song => song.songNumber === songNumber);
          
          if (songInQueue) {
            songsToLoad[i] = songInQueue;
          } else {
            // If not in queue, try to fetch it
            try {
              const songId = songNumber.toString().padStart(3, '0');
              const song = await fetchSongById(songId);
              if (song) {
                songsToLoad[i] = song;
              } else {
                songsToLoad[i] = null;
              }
            } catch (error) {
              console.error(`Error fetching song #${songNumber}:`, error);
              songsToLoad[i] = null;
            }
          }
        }
        
        setWeekSongs(songsToLoad);
      } catch (error) {
        console.error('Error loading week songs:', error);
      } finally {
        setIsLoadingSongs(false);
      }
    };
    
    loadWeekSongs();
  }, [currentWeek, queue]);
  
  // Handle song press with proper event handling
  const handleSongPress = (song: Song | null) => {
    if (song) {
      setCurrentSong(song);
      // Navigate to player screen
      router.push('/player');
    }
  };
  
  if (programLoading || isLoadingSongs) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color={Colors.dark.primary} />
        <Text style={styles.loadingText}>
          {programLoading ? 'Loading weekly program...' : 'Loading hymns...'}
        </Text>
      </View>
    );
  }
  
  if (!currentWeek || !currentWeek.songs) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No weekly program available</Text>
      </View>
    );
  }
  
  // Helper function to check if an object is a Firestore timestamp
  const isFirestoreTimestamp = (obj: any): obj is FirestoreTimestamp => {
    return obj && typeof obj === 'object' && 'toDate' in obj && typeof obj.toDate === 'function';
  };
  
  // Format the week start date
  const formatWeekDate = () => {
    if (!currentWeek.weekNumber) return 'Current Week';
    
    // If no startDate or invalid startDate, just show the week number
    if (!currentWeek.startDate) return `Week ${currentWeek.weekNumber}`;
    
    try {
      // Handle different date formats
      let startDate: Date;
      
      if (typeof currentWeek.startDate === 'string') {
        startDate = new Date(currentWeek.startDate);
      } else if (isFirestoreTimestamp(currentWeek.startDate)) {
        // Handle Firestore Timestamp
        startDate = currentWeek.startDate.toDate();
      } else if (currentWeek.startDate instanceof Date) {
        startDate = currentWeek.startDate;
      } else {
        // Unknown format, return just the week number
        return `Week ${currentWeek.weekNumber}`;
      }
      
      // Check if date is valid
      if (isNaN(startDate.getTime())) {
        return `Week ${currentWeek.weekNumber}`;
      }
      
      // Ensure we're using Monday as the start of the week
      const dayOfWeek = startDate.getUTCDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
      
      // If startDate is not Monday, adjust to the Monday of this week
      let mondayDate = new Date(startDate);
      if (dayOfWeek !== 1) {
        // Calculate days to subtract to get to Monday
        // If Sunday (0), go back 6 days to previous Monday
        // If Tuesday through Saturday (2-6), go back (dayOfWeek - 1) days
        const daysToAdjust = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
        mondayDate.setUTCDate(mondayDate.getUTCDate() - daysToAdjust);
      }
      
      // Calculate end date (Sunday) by adding 6 days to Monday
      const endDate = new Date(mondayDate);
      endDate.setUTCDate(endDate.getUTCDate() + 6); // Monday + 6 days = Sunday
      
      const startDay = mondayDate.getUTCDate();
      const endDay = endDate.getUTCDate();
      const startMonth = mondayDate.toLocaleString('default', { month: 'short' });
      const endMonth = endDate.toLocaleString('default', { month: 'short' });
      
      // If start and end months are different, include both
      if (startMonth !== endMonth) {
        return `Week ${currentWeek.weekNumber} (${startDay} ${startMonth} - ${endDay} ${endMonth})`;
      }
      
      return `Week ${currentWeek.weekNumber} (${startDay}-${endDay} ${startMonth})`;
    } catch (error) {
      console.error('Error formatting week date:', error);
      return `Week ${currentWeek.weekNumber}`;
    }
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <Calendar size={18} color={Colors.dark.primary} />
        <Text style={styles.headerText}>{formatWeekDate()}</Text>
      </View>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {currentWeek.songs.map((songNumber, index) => {
          const song = weekSongs[index];
          const dayName = formatDayOfWeek(index);
          const isToday = todayIndex === index; // Use our pre-calculated Monday-based day index
          
          return (
            <Pressable 
              key={index}
              onPress={() => handleSongPress(song)}
              disabled={!song}
            >
              <LinearGradient
                colors={isToday 
                  ? ['rgba(226, 177, 60, 0.3)', 'rgba(226, 177, 60, 0.1)'] 
                  : [Colors.dark.surface, Colors.dark.surface]}
                style={[styles.dayCard, isToday && styles.todayCard]}
              >
                <Text style={[styles.dayName, isToday && styles.todayText]}>{dayName}</Text>
                
                <View style={[styles.numberCircle, isToday && styles.todayNumberCircle]}>
                  <Text style={[styles.numberText, isToday && styles.todayNumberText]}>
                    {songNumber}
                  </Text>
                </View>
                
                {song ? (
                  <Text style={[styles.songTitle, isToday && styles.todayText]} numberOfLines={2}>
                    {song.title}
                  </Text>
                ) : (
                  <Text style={styles.loadingTitle}>Loading...</Text>
                )}
                
                {isToday && (
                  <View style={styles.todayBadge}>
                    <Text style={styles.todayBadgeText}>Today</Text>
                  </View>
                )}
              </LinearGradient>
            </Pressable>
          );
        })}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    color: Colors.dark.text,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  scrollContent: {
    paddingRight: 16,
    paddingBottom: 8,
  },
  dayCard: {
    width: 130,
    height: 170,
    borderRadius: 16,
    padding: 16,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(248, 250, 252, 0.1)',
  },
  todayCard: {
    borderColor: Colors.dark.primary,
    borderWidth: 1,
  },
  dayName: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 12,
    textTransform: 'uppercase',
  },
  todayText: {
    color: Colors.dark.primary,
  },
  numberCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(248, 250, 252, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  todayNumberCircle: {
    backgroundColor: Colors.dark.primary,
  },
  numberText: {
    color: Colors.dark.text,
    fontSize: 16,
    fontWeight: 'bold',
  },
  todayNumberText: {
    color: Colors.dark.background,
  },
  songTitle: {
    color: Colors.dark.text,
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '500',
  },
  loadingTitle: {
    color: Colors.dark.textTertiary,
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  todayBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: Colors.dark.primary,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  todayBadgeText: {
    color: Colors.dark.background,
    fontSize: 10,
    fontWeight: 'bold',
  },
  loadingContainer: {
    backgroundColor: Colors.dark.surface,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyContainer: {
    backgroundColor: Colors.dark.surface,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginBottom: 24,
  },
  loadingText: {
    color: Colors.dark.textSecondary,
    marginTop: 8,
    fontSize: 14,
  },
  emptyText: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    textAlign: 'center',
  },
});