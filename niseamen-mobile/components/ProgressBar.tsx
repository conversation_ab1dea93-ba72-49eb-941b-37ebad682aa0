import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, Pressable, GestureResponderEvent, LayoutChangeEvent } from 'react-native';
import { usePlayerStore } from '@/stores/player-store';
import Colors from '@/constants/colors';

export default function ProgressBar() {
  const { currentSong, seekToTime, getProgress } = usePlayerStore();
  const [isSeeking, setIsSeeking] = useState(false);
  const [seekPosition, setSeekPosition] = useState(0);
  const progressContainerRef = useRef(null);
  const progressContainerWidth = useRef(0);
  
  if (!currentSong) return null;
  
  const { currentTime, duration } = getProgress();
  const progress = isSeeking ? seekPosition : (duration > 0 ? (currentTime / duration) : 0);
  
  const formatTime = (seconds: number) => {
    if (!seconds || isNaN(seconds)) {
      return "0:00";
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };
  
  const handleSeek = (event: GestureResponderEvent) => {
    if (!progressContainerWidth.current) return;
    
    const { locationX } = event.nativeEvent;
    const position = Math.max(0, Math.min(1, locationX / progressContainerWidth.current));
    setSeekPosition(position);
  };
  
  const handleSeekComplete = () => {
    setIsSeeking(false);
    if (duration > 0) {
      seekToTime(seekPosition * duration);
    }
  };
  
  const handleLayout = (event: LayoutChangeEvent) => {
    progressContainerWidth.current = event.nativeEvent.layout.width;
  };
  
  return (
    <View style={styles.container}>
      <Pressable 
        ref={progressContainerRef}
        style={styles.progressContainer}
        onTouchStart={() => setIsSeeking(true)}
        onTouchMove={handleSeek}
        onTouchEnd={handleSeekComplete}
        onLayout={handleLayout}
      >
        <View style={styles.progressBackground} />
        <View 
          style={[
            styles.progressFill,
            { width: `${progress * 100}%` }
          ]} 
        />
        <View 
          style={[
            styles.progressThumb,
            { left: `${progress * 100}%` }
          ]} 
        />
      </Pressable>
      
      <View style={styles.timeContainer}>
        <Text style={styles.timeText}>
          {formatTime(currentTime)}
        </Text>
        <Text style={styles.timeText}>
          {formatTime(duration)}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingHorizontal: 8,
    marginBottom: 8,
  },
  progressContainer: {
    height: 20,
    justifyContent: 'center',
  },
  progressBackground: {
    height: 4,
    backgroundColor: 'rgba(248, 250, 252, 0.1)',
    borderRadius: 2,
  },
  progressFill: {
    position: 'absolute',
    height: 4,
    backgroundColor: Colors.dark.primary,
    borderRadius: 2,
  },
  progressThumb: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.dark.primary,
    marginLeft: -6,
    top: 4,
    borderWidth: 2,
    borderColor: Colors.dark.background,
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  timeText: {
    color: Colors.dark.textSecondary,
    fontSize: 12,
  },
});
