import React from 'react';
import { View, Text, StyleSheet, Pressable, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { Play, Pause } from 'lucide-react-native';
import { usePlayerStore } from '@/stores/player-store';
import Colors from '@/constants/colors';
import { LinearGradient } from 'expo-linear-gradient';

export default function MiniPlayer() {
  const router = useRouter();
  const { currentSong, isPlaying, playPause, getProgress } = usePlayerStore();
  
  if (!currentSong) return null;

  const handlePress = () => {
    router.push('/player');
  };
  
  return (
    <LinearGradient 
      colors={[Colors.dark.surface, 'rgba(30, 41, 59, 0.95)']} 
      style={styles.container}
    >
      <Pressable 
        style={[styles.content, !isPlaying && styles.buttonEffect]}
        android_ripple={{ color: 'rgba(255, 255, 255, 0.1)' }}
        onPress={handlePress}
      >
        <View style={styles.mainContent}>
          <View style={styles.songInfo}>
            <Image 
              source={currentSong?.imageUrl ? {
                uri: currentSong.imageUrl,
                cache: 'force-cache'
              } : require('@/assets/images/icon.png')}
              style={styles.image}
              fadeDuration={0}
            />
            <View style={styles.textContainer}>
              <Text style={styles.title} numberOfLines={1}>
                {currentSong?.title || 'Loading...'}
              </Text>
              <Text style={styles.artist} numberOfLines={1}>
                {currentSong?.artist || 'Unknown Artist'}
              </Text>
            </View>
          </View>

          <Pressable 
            style={styles.playButton}
            onPress={(e) => {
              e.stopPropagation();
              playPause();
            }}
          >
            {isPlaying ? (
              <Pause size={20} color={Colors.dark.background} />
            ) : (
              <Play size={20} color={Colors.dark.background} />
            )}
          </Pressable>
        </View>

        <View style={styles.progressBarContainer}>
          <View 
            style={[
              styles.progressBar,
              { width: `${(getProgress().currentTime / getProgress().duration) * 100}%` }
            ]}
          />
        </View>
      </Pressable>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 60,
    left: 0,
    right: 0,
    zIndex: 100,
    borderTopWidth: 1,
    borderTopColor: 'rgba(226, 177, 60, 0.3)',
  },
  content: {
    paddingTop: 8,
    paddingBottom: 4,
    paddingHorizontal: 16,
  },
  buttonEffect: {
    opacity: 0.8,
  },
  mainContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  songInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  image: {
    width: 40,
    height: 40,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: 'rgba(226, 177, 60, 0.3)',
  },
  textContainer: {
    flex: 1,
    marginLeft: 12,
  },
  title: {
    color: Colors.dark.text,
    fontSize: 14,
    fontWeight: '600',
  },
  artist: {
    color: Colors.dark.textSecondary,
    fontSize: 12,
  },
  playButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.dark.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressBarContainer: {
    height: 2,
    backgroundColor: 'rgba(248, 250, 252, 0.1)',
    marginTop: 8,
    width: '100%',
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.dark.primary,
  },
});
