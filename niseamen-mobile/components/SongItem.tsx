import React from 'react';
import { View, Text, StyleSheet, Pressable, Image } from 'react-native';
import { usePlayerStore } from '@/stores/player-store';
import { Song } from '@/types/song';
import Colors from '@/constants/colors';
import { Music, Play } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

interface SongItemProps {
  song: Song;
  showLyrics?: boolean;
  searchQuery?: string;
  compact?: boolean;
  autoNavigate?: boolean;
}

export default function SongItem({ 
  song, 
  showLyrics = false, 
  searchQuery = '', 
  compact = false,
  autoNavigate = true
}: SongItemProps) {
  const { currentSong, setCurrentSong } = usePlayerStore();
  const isActive = currentSong?.id === song.id;
  const router = useRouter();
  
  const formatDuration = (seconds?: number) => {
    if (!seconds || isNaN(seconds)) {
      return "0:00";
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };
  
  // Find matching lyrics snippet if search query exists
  const getLyricsSnippet = () => {
    if (!searchQuery || !showLyrics) return null;
    
    const lowerQuery = searchQuery.toLowerCase();
    let matchedLyrics = '';
    
    // Check all languages for matches
    for (const language in song.lyrics) {
      const lyrics = song.lyrics[language];
      const lowerLyrics = lyrics.toLowerCase();
      
      if (lowerLyrics.includes(lowerQuery)) {
        // Find the line containing the match
        const lines = lyrics.split('\n');
        for (const line of lines) {
          if (line.toLowerCase().includes(lowerQuery)) {
            matchedLyrics = line;
            break;
          }
        }
        if (matchedLyrics) break;
      }
    }
    
    return matchedLyrics;
  };
  
  const lyricsSnippet = getLyricsSnippet();
  
  const handlePress = () => {
    setCurrentSong(song);
    // Navigate to player screen if autoNavigate is true
    if (autoNavigate) {
      router.push('/player');
    }
  };
  
  // Display the song title without redundant numbering
  const displayTitle = () => {
    // Just use the title as it is from the database
    return song.title;
  };
  
  // Render compact version for recently played
  if (compact) {
    return (
      <Pressable onPress={handlePress}>
        <LinearGradient
          colors={isActive 
            ? ['rgba(226, 177, 60, 0.3)', 'rgba(30, 41, 59, 0.8)'] 
            : [Colors.dark.surface, Colors.dark.surface]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[styles.compactContainer, isActive && styles.activeCompactContainer]}
        >
          <View style={styles.compactLeftSection}>
            <Image 
              source={song.imageUrl ? {
                uri: song.imageUrl,
                cache: 'force-cache'
              } : require('@/assets/images/icon.png')}
              style={styles.compactImage} 
              fadeDuration={0}
            />
            <View style={styles.compactTextContainer}>
              <Text style={[styles.compactTitle, isActive && styles.activeText]} numberOfLines={1}>
                {displayTitle()}
              </Text>
              <Text style={styles.compactArtist} numberOfLines={1}>
                {song.artist}
              </Text>
            </View>
          </View>
          
          <View style={styles.compactPlayButton}>
            <Play size={16} color={isActive ? Colors.dark.background : Colors.dark.text} />
          </View>
        </LinearGradient>
      </Pressable>
    );
  }
  
  return (
    <Pressable onPress={handlePress}>
      <LinearGradient
        colors={isActive 
          ? ['rgba(226, 177, 60, 0.3)', 'rgba(30, 41, 59, 0.8)'] 
          : [Colors.dark.surface, Colors.dark.surface]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={[styles.container, isActive && styles.activeContainer]}
      >
        <View style={styles.leftSection}>
          <Image 
            source={song.imageUrl ? {
              uri: song.imageUrl,
              cache: 'force-cache'
            } : require('@/assets/images/icon.png')}
            style={styles.image}
            fadeDuration={0}
          />
          <View style={styles.textContainer}>
            <Text style={[styles.title, isActive && styles.activeText]} numberOfLines={1}>
              {displayTitle()}
            </Text>
            <Text style={styles.artist} numberOfLines={1}>
              {song.artist}
            </Text>
            {lyricsSnippet && (
              <Text style={styles.lyrics} numberOfLines={1}>
                "{lyricsSnippet}"
              </Text>
            )}
          </View>
        </View>
        
        <View style={styles.rightSection}>
          <Text style={styles.duration}>{formatDuration(song.duration)}</Text>
          <View style={[styles.playButton, isActive && styles.activePlayButton]}>
            <Play size={16} color={isActive ? Colors.dark.background : Colors.dark.primary} />
          </View>
        </View>
      </LinearGradient>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'rgba(248, 250, 252, 0.05)',
  },
  activeContainer: {
    borderColor: Colors.dark.primary,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  rightSection: {
    alignItems: 'flex-end',
  },
  image: {
    width: 48,
    height: 48,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(226, 177, 60, 0.3)',
  },
  imagePlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: 'rgba(248, 250, 252, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    marginLeft: 12,
    flex: 1,
  },
  title: {
    color: Colors.dark.text,
    fontSize: 16,
    fontWeight: '600',
  },
  activeText: {
    color: Colors.dark.primary,
  },
  artist: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    marginTop: 2,
  },
  lyrics: {
    color: Colors.dark.textTertiary,
    fontSize: 12,
    marginTop: 4,
    fontStyle: 'italic',
  },
  duration: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    marginBottom: 4,
  },
  playButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(226, 177, 60, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activePlayButton: {
    backgroundColor: Colors.dark.primary,
  },
  
  // Compact styles
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'rgba(248, 250, 252, 0.05)',
  },
  activeCompactContainer: {
    borderColor: Colors.dark.primary,
  },
  compactLeftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  compactImage: {
    width: 40,
    height: 40,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: 'rgba(226, 177, 60, 0.3)',
  },
  compactImagePlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 6,
    backgroundColor: 'rgba(248, 250, 252, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  compactTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  compactTitle: {
    color: Colors.dark.text,
    fontSize: 14,
    fontWeight: '600',
  },
  compactArtist: {
    color: Colors.dark.textSecondary,
    fontSize: 12,
    marginTop: 2,
  },
  compactPlayButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(226, 177, 60, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
