import React from 'react';
import { View, StyleSheet, Pressable } from 'react-native';
import { Play, Pause, SkipBack, Ski<PERSON><PERSON>orward, Repeat, Shuffle } from 'lucide-react-native';
import { usePlayerStore } from '@/stores/player-store';
import Colors from '@/constants/colors';

interface PlayerControlsProps {
  size?: 'small' | 'large';
  minimal?: boolean;
}

export default function PlayerControls({ size = 'large', minimal = false }: PlayerControlsProps) {
  const { isPlaying, playPause, nextSong, previousSong } = usePlayerStore();
  
  const isLarge = size === 'large';
  
  if (minimal) {
    return (
      <View style={styles.minimalContainer}>
        <Pressable 
          style={styles.minimalSideButton} 
          onPress={previousSong}
        >
          <SkipBack size={18} color={Colors.dark.text} />
        </Pressable>
        
        <Pressable 
          style={styles.minimalPlayButton} 
          onPress={playPause}
        >
          {isPlaying ? (
            <Pause size={18} color={Colors.dark.background} />
          ) : (
            <Play size={18} color={Colors.dark.background} />
          )}
        </Pressable>
        
        <Pressable 
          style={styles.minimalSideButton} 
          onPress={nextSong}
        >
          <SkipForward size={18} color={Colors.dark.text} />
        </Pressable>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <Pressable style={styles.secondaryButton}>
        <Shuffle size={isLarge ? 20 : 16} color={Colors.dark.textSecondary} />
      </Pressable>
      
      <View style={styles.primaryControls}>
        <Pressable 
          style={[styles.sideButton, isLarge && styles.largeSideButton]} 
          onPress={previousSong}
        >
          <SkipBack 
            size={isLarge ? 24 : 20} 
            color={Colors.dark.text} 
          />
        </Pressable>
        
        <Pressable 
          style={[styles.playButton, isLarge && styles.largePlayButton]} 
          onPress={playPause}
        >
          {isPlaying ? (
            <Pause 
              size={isLarge ? 28 : 24} 
              color={Colors.dark.background} 
            />
          ) : (
            <Play 
              size={isLarge ? 28 : 24} 
              color={Colors.dark.background} 
            />
          )}
        </Pressable>
        
        <Pressable 
          style={[styles.sideButton, isLarge && styles.largeSideButton]} 
          onPress={nextSong}
        >
          <SkipForward 
            size={isLarge ? 24 : 20} 
            color={Colors.dark.text} 
          />
        </Pressable>
      </View>
      
      <Pressable style={styles.secondaryButton}>
        <Repeat size={isLarge ? 20 : 16} color={Colors.dark.textSecondary} />
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  primaryControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  playButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.dark.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 16,
  },
  largePlayButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    marginHorizontal: 20,
  },
  sideButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(248, 250, 252, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  largeSideButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  secondaryButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  // Minimal controls for full-screen lyrics view
  minimalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  minimalPlayButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.dark.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 16,
  },
  minimalSideButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(248, 250, 252, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});