import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Pressable, Image, ActivityIndicator } from 'react-native';
import { usePlayerStore } from '@/stores/player-store';
import { useProgramStore } from '@/stores/program-store';
import Colors from '@/constants/colors';
import { Music, Calendar, Play, RefreshCw } from 'lucide-react-native';
import { fetchSongById } from '@/services/songs';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Song } from '@/types/song';

const DEFAULT_IMAGE = require('@/assets/images/icon.png');

export default function FeaturedSong() {
  const { currentFeaturedSong, isLoading: programLoading } = useProgramStore();
  const { queue, setCurrentSong } = usePlayerStore();
  const [formattedDate, setFormattedDate] = useState('');
  const [featuredSong, setFeaturedSong] = useState<Song | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  
  // Format the date once when component mounts
  useEffect(() => {
    const today = new Date();
    // Add 1 hour to get GMT+1
    const gmtPlus1 = new Date(today.getTime() + (3600000));
    
    const formatted = gmtPlus1.toLocaleDateString('en-US', { 
      weekday: 'long', 
      month: 'long', 
      day: 'numeric',
      timeZone: 'UTC' // Use UTC since we've already adjusted the time
    });
    
    setFormattedDate(formatted);
  }, []);
  
  // Use a ref to track if we've already loaded this featured song
  const loadedSongRef = React.useRef<number | null>(null);
  
  // Load the featured song when currentFeaturedSong changes
  useEffect(() => {
    // Skip if we've already loaded this song number
    if (loadedSongRef.current === currentFeaturedSong && featuredSong?.id) {
      return;
    }
    
    const loadFeaturedSong = async () => {
      if (!currentFeaturedSong) return;
      
      setIsLoading(true);
      
      try {
        // First try to find the song in the queue by song number
        // Check if queue exists and is an array before searching
        const songInQueue = Array.isArray(queue) 
          ? queue.find(song => song?.songNumber === currentFeaturedSong)
          : null;
        
        if (songInQueue?.id) {
          // Only log once when we first find it
          setFeaturedSong(songInQueue);
          loadedSongRef.current = currentFeaturedSong;
          setIsLoading(false);
          return;
        }
        
        // If not found in queue by number, try to fetch it directly by ID
        // Convert to string ID for Firestore lookup
        const songId = currentFeaturedSong.toString().padStart(3, '0');
        const song = await fetchSongById(songId);
        
        if (song?.id) {
          setFeaturedSong(song);
          loadedSongRef.current = currentFeaturedSong;
        } else {
        }
      } catch (error) {
        console.error(`Error loading featured song #${currentFeaturedSong}:`, error);
      } finally {
        setIsLoading(false);
      }
    };
    
    if (currentFeaturedSong) {
      loadFeaturedSong();
    }
  }, [currentFeaturedSong, queue, featuredSong]);
  
  const handlePlaySong = () => {
    if (featuredSong?.id) {
      try {
        setCurrentSong(featuredSong);
        // Navigate to player screen
        router.push('/player');
      } catch (error) {
        console.error('Error playing featured song:', error);
      }
    }
  };
  
  if (programLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator color={Colors.dark.primary} size="small" />
        <Text style={styles.loadingText}>Loading today's hymn...</Text>
      </View>
    );
  }
  
  if (!currentFeaturedSong) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No hymn featured for today</Text>
      </View>
    );
  }
  
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator color={Colors.dark.primary} size="small" />
        <Text style={styles.loadingText}>Loading hymn #{currentFeaturedSong}...</Text>
      </View>
    );
  }
  
  if (!featuredSong?.id) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>
          Hymn #{currentFeaturedSong} not available yet
        </Text>
      </View>
    );
  }
  
  return (
    <Pressable onPress={handlePlaySong}>
      <LinearGradient
        colors={[Colors.dark.surface, 'rgba(30, 41, 59, 0.4)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.container}
      >
        <View style={styles.dateRow}>
          <View style={styles.dateInfo}>
            <Calendar size={16} color={Colors.dark.primary} />
            <Text style={styles.dateText}>{formattedDate}</Text>
          </View>
          <Pressable 
            style={styles.refreshButton}
            onPress={() => {
              const { loadProgram } = useProgramStore.getState();
              setIsLoading(true);
              loadProgram(true).finally(() => setIsLoading(false));
            }}
          >
            <RefreshCw size={16} color={Colors.dark.primary} />
          </Pressable>
        </View>
        
        <View style={styles.contentRow}>
          <Image 
            source={DEFAULT_IMAGE}
            style={styles.albumArt} 
          />
          
          <View style={styles.songInfo}>
            <View style={styles.numberBadge}>
              <Text style={styles.numberText}>#{featuredSong?.songNumber || ''}</Text>
            </View>
            
            <Text style={styles.songTitle} numberOfLines={2}>
              {featuredSong?.title || 'Untitled'}
            </Text>
            
            <Text style={styles.artistName} numberOfLines={1}>
              {featuredSong?.artist || 'Unknown Artist'}
            </Text>
          </View>
        </View>
        
        <Pressable 
          style={styles.playButton} 
          onPress={handlePlaySong}
          disabled={!featuredSong?.id}
        >
          <Play size={20} color={Colors.dark.background} />
          <Text style={styles.playText}>Play Today's Hymn</Text>
        </Pressable>
      </LinearGradient>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: 'rgba(226, 177, 60, 0.3)',
  },
  loadingContainer: {
    backgroundColor: Colors.dark.surface,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyContainer: {
    backgroundColor: Colors.dark.surface,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginBottom: 24,
  },
  loadingText: {
    color: Colors.dark.textSecondary,
    marginTop: 8,
    fontSize: 14,
  },
  emptyText: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    textAlign: 'center',
  },
  dateRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  dateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    marginLeft: 8,
    fontStyle: 'italic',
  },
  refreshButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(226, 177, 60, 0.1)',
  },
  contentRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  albumArt: {
    width: 100,
    height: 100,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(226, 177, 60, 0.5)',
  },
  songInfo: {
    flex: 1,
    marginLeft: 16,
    justifyContent: 'center',
  },
  numberBadge: {
    backgroundColor: Colors.dark.primary,
    alignSelf: 'flex-start',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  numberText: {
    color: Colors.dark.background,
    fontSize: 12,
    fontWeight: 'bold',
  },
  songTitle: {
    color: Colors.dark.text,
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  artistName: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
  },
  playButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.dark.primary,
    borderRadius: 24,
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  playText: {
    color: Colors.dark.background,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
});
