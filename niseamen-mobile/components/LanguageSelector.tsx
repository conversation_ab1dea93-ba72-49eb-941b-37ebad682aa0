import React, { useState } from 'react';
import { View, Text, StyleSheet, Pressable, Modal } from 'react-native';
import { ChevronDown, Globe } from 'lucide-react-native';
import { usePlayerStore } from '@/stores/player-store';
import Colors from '@/constants/colors';
import { Language } from '@/types/song';

const languages: { [key in Language]: string } = {
  goun: 'Goun',
  french: 'Français'
};

interface LanguageSelectorProps {
  compact?: boolean;
}

export default function LanguageSelector({ compact = false }: LanguageSelectorProps) {
  const { selectedLanguage, setSelectedLanguage } = usePlayerStore();
  const [modalVisible, setModalVisible] = useState(false);
  
  const handleSelectLanguage = (language: string) => {
    setSelectedLanguage(language);
    setModalVisible(false);
  };
  
  return (
    <View style={styles.container}>
      <Pressable 
        style={[styles.selector, compact && styles.compactSelector]} 
        onPress={() => setModalVisible(true)}
      >
        <Globe size={compact ? 12 : 14} color={Colors.dark.primary} />
        <Text style={[styles.selectorText, compact && styles.compactSelectorText]}>
          {languages[selectedLanguage as Language] || selectedLanguage}
        </Text>
        <ChevronDown size={compact ? 12 : 14} color={Colors.dark.primary} />
      </Pressable>
      
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <Pressable 
          style={styles.modalOverlay}
          onPress={() => setModalVisible(false)}
        >
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Select Language</Text>
            
            {Object.entries(languages).map(([key, value]) => (
              <Pressable
                key={key}
                style={[
                  styles.languageOption,
                  selectedLanguage === key && styles.selectedLanguage
                ]}
                onPress={() => handleSelectLanguage(key)}
              >
                <Text 
                  style={[
                    styles.languageText,
                    selectedLanguage === key && styles.selectedLanguageText
                  ]}
                >
                  {value}
                </Text>
                
                {selectedLanguage === key && (
                  <View style={styles.checkmark} />
                )}
              </Pressable>
            ))}
          </View>
        </Pressable>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginLeft: 'auto',
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(226, 177, 60, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  compactSelector: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  selectorText: {
    color: Colors.dark.primary,
    marginHorizontal: 6,
    fontSize: 14,
    fontWeight: '500',
  },
  compactSelectorText: {
    fontSize: 12,
    marginHorizontal: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(15, 23, 42, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors.dark.surface,
    borderRadius: 16,
    padding: 20,
    width: '80%',
    maxWidth: 300,
    borderWidth: 1,
    borderColor: 'rgba(226, 177, 60, 0.3)',
  },
  modalTitle: {
    color: Colors.dark.text,
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedLanguage: {
    backgroundColor: 'rgba(226, 177, 60, 0.2)',
  },
  languageText: {
    color: Colors.dark.text,
    fontSize: 16,
  },
  selectedLanguageText: {
    color: Colors.dark.primary,
    fontWeight: '600',
  },
  checkmark: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.dark.primary,
  },
});