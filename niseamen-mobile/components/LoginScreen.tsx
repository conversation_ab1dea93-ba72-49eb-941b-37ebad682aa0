import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TextInput, 
  Pressable, 
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  Image
} from 'react-native';
import { useUserStore } from '@/stores/user-store';
import Colors from '@/constants/colors';
import { Mail, Lock, User, ArrowRight } from 'lucide-react-native';
import GoogleSignInButton from './GoogleSignInButton';
import { useGoogleAuth } from '@/services/auth';
import * as WebBrowser from 'expo-web-browser';
import { LinearGradient } from 'expo-linear-gradient';
import { Image as ExpoImage } from 'expo-image';

// Initialize WebBrowser for Google Auth
WebBrowser.maybeCompleteAuthSession();

enum AuthMode {
  LOGIN = 'login',
  REGISTER = 'register',
  FORGOT_PASSWORD = 'forgot_password'
}

export default function LoginScreen() {
  const { login, register, resetPassword, signInWithGoogle, isLoading, error, clearError } = useUserStore();
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [mode, setMode] = useState<AuthMode>(AuthMode.LOGIN);
  const [googleLoading, setGoogleLoading] = useState(false);
  
  // Google Auth
  const [request, response, promptAsync] = useGoogleAuth();
  
  // Handle Google Sign-In response
  useEffect(() => {
    if (response?.type === 'success' && response.authentication) {
      handleGoogleSignIn(response.authentication.accessToken);
    } else if (response?.type === 'error') {
      Alert.alert('Google Sign-In Error', 'Failed to sign in with Google. Please try again.');
      setGoogleLoading(false);
    }
  }, [response]);
  
  // Clear error when changing modes
  useEffect(() => {
    clearError();
  }, [mode]);
  
  // Show error alert if there's an error
  useEffect(() => {
    if (error) {
      Alert.alert('Error', error);
      clearError();
    }
  }, [error]);
  
  const handleSubmit = async () => {
    try {
      if (mode === AuthMode.LOGIN) {
        await login(email, password);
      } else if (mode === AuthMode.REGISTER) {
        if (!displayName.trim()) {
          Alert.alert('Error', 'Please enter your name');
          return;
        }
        await register(email, password, displayName);
      } else if (mode === AuthMode.FORGOT_PASSWORD) {
        await resetPassword(email);
        Alert.alert('Success', 'Password reset email sent. Please check your inbox.');
        setMode(AuthMode.LOGIN);
      }
    } catch (error: any) {
      // Error is handled by the store
    }
  };
  
  const handleGoogleSignIn = async (accessToken: string) => {
    try {
      setGoogleLoading(true);
      await signInWithGoogle(accessToken);
    } catch (error: any) {
      Alert.alert('Google Sign-In Error', error.message || 'Failed to sign in with Google');
    } finally {
      setGoogleLoading(false);
    }
  };
  
  const handleGooglePress = async () => {
    if (!request) {
      Alert.alert('Google Sign-In', 'Google Sign-In is not ready yet. Please try again.');
      return;
    }
    
    setGoogleLoading(true);
    await promptAsync();
  };
  
  const toggleMode = () => {
    if (mode === AuthMode.LOGIN) {
      setMode(AuthMode.REGISTER);
    } else {
      setMode(AuthMode.LOGIN);
    }
  };
  
  const renderForm = () => {
    switch (mode) {
      case AuthMode.LOGIN:
        return (
          <>
            <View style={styles.logoContainer}>
              <View style={styles.logoBackground}>
                <ExpoImage
                  source={require('../assets/images/icon.png')}
                  style={styles.logoImage}
                  contentFit="cover"
                />
              </View>
              <Text style={styles.appName}>NiseAmen</Text>
            </View>
            
            <Text style={styles.title}>Welcome Back</Text>
            <Text style={styles.subtitle}>Sign in to continue</Text>
            
            <View style={styles.inputContainer}>
              <Mail size={20} color={Colors.dark.textSecondary} />
              <TextInput
                style={styles.input}
                placeholder="Email"
                placeholderTextColor={Colors.dark.textSecondary}
                value={email}
                onChangeText={setEmail}
                autoCapitalize="none"
                keyboardType="email-address"
              />
            </View>
            
            <View style={styles.inputContainer}>
              <Lock size={20} color={Colors.dark.textSecondary} />
              <TextInput
                style={styles.input}
                placeholder="Password"
                placeholderTextColor={Colors.dark.textSecondary}
                value={password}
                onChangeText={setPassword}
                secureTextEntry
              />
            </View>
            
            <Pressable 
              style={styles.forgotPassword}
              onPress={() => setMode(AuthMode.FORGOT_PASSWORD)}
            >
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </Pressable>
            
            <Pressable 
              style={styles.submitButton}
              onPress={handleSubmit}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={Colors.dark.background} />
              ) : (
                <>
                  <Text style={styles.submitButtonText}>Sign In</Text>
                  <ArrowRight size={20} color={Colors.dark.background} />
                </>
              )}
            </Pressable>
            
            <View style={styles.dividerContainer}>
              <View style={styles.divider} />
              <Text style={styles.dividerText}>OR</Text>
              <View style={styles.divider} />
            </View>
            
            <GoogleSignInButton 
              onPress={handleGooglePress} 
              disabled={googleLoading || isLoading}
            />
            
            <View style={styles.toggleContainer}>
              <Text style={styles.toggleText}>Don't have an account?</Text>
              <Pressable onPress={toggleMode}>
                <Text style={styles.toggleButton}>Sign Up</Text>
              </Pressable>
            </View>
          </>
        );
        
      case AuthMode.REGISTER:
        return (
          <>
            <View style={styles.logoContainer}>
              <View style={styles.logoBackground}>
                <ExpoImage
                  source={require('../assets/images/icon.png')}
                  style={styles.logoImage}
                  contentFit="cover"
                />
              </View>
              <Text style={styles.appName}>NiseAmen</Text>
            </View>
            
            <Text style={styles.title}>Create Account</Text>
            <Text style={styles.subtitle}>Sign up to get started</Text>
            
            <View style={styles.inputContainer}>
              <User size={20} color={Colors.dark.textSecondary} />
              <TextInput
                style={styles.input}
                placeholder="Full Name"
                placeholderTextColor={Colors.dark.textSecondary}
                value={displayName}
                onChangeText={setDisplayName}
              />
            </View>
            
            <View style={styles.inputContainer}>
              <Mail size={20} color={Colors.dark.textSecondary} />
              <TextInput
                style={styles.input}
                placeholder="Email"
                placeholderTextColor={Colors.dark.textSecondary}
                value={email}
                onChangeText={setEmail}
                autoCapitalize="none"
                keyboardType="email-address"
              />
            </View>
            
            <View style={styles.inputContainer}>
              <Lock size={20} color={Colors.dark.textSecondary} />
              <TextInput
                style={styles.input}
                placeholder="Password"
                placeholderTextColor={Colors.dark.textSecondary}
                value={password}
                onChangeText={setPassword}
                secureTextEntry
              />
            </View>
            
            <Pressable 
              style={styles.submitButton}
              onPress={handleSubmit}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={Colors.dark.background} />
              ) : (
                <>
                  <Text style={styles.submitButtonText}>Sign Up</Text>
                  <ArrowRight size={20} color={Colors.dark.background} />
                </>
              )}
            </Pressable>
            
            <View style={styles.dividerContainer}>
              <View style={styles.divider} />
              <Text style={styles.dividerText}>OR</Text>
              <View style={styles.divider} />
            </View>
            
            <GoogleSignInButton 
              onPress={handleGooglePress} 
              disabled={googleLoading || isLoading}
            />
            
            <View style={styles.toggleContainer}>
              <Text style={styles.toggleText}>Already have an account?</Text>
              <Pressable onPress={toggleMode}>
                <Text style={styles.toggleButton}>Sign In</Text>
              </Pressable>
            </View>
          </>
        );
        
      case AuthMode.FORGOT_PASSWORD:
        return (
          <>
            <View style={styles.logoContainer}>
              <View style={styles.logoBackground}>
                <ExpoImage
                  source={require('../assets/images/icon.png')}
                  style={styles.logoImage}
                  contentFit="cover"
                />
              </View>
              <Text style={styles.appName}>NiseAmen</Text>
            </View>
            
            <Text style={styles.title}>Reset Password</Text>
            <Text style={styles.subtitle}>We'll send you a reset link</Text>
            
            <View style={styles.inputContainer}>
              <Mail size={20} color={Colors.dark.textSecondary} />
              <TextInput
                style={styles.input}
                placeholder="Email"
                placeholderTextColor={Colors.dark.textSecondary}
                value={email}
                onChangeText={setEmail}
                autoCapitalize="none"
                keyboardType="email-address"
              />
            </View>
            
            <Pressable 
              style={styles.submitButton}
              onPress={handleSubmit}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={Colors.dark.background} />
              ) : (
                <Text style={styles.submitButtonText}>Send Reset Link</Text>
              )}
            </Pressable>
            
            <Pressable 
              style={styles.backButton}
              onPress={() => setMode(AuthMode.LOGIN)}
            >
              <Text style={styles.backButtonText}>Back to Sign In</Text>
            </Pressable>
          </>
        );
    }
  };
  
  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <LinearGradient
        colors={[Colors.dark.background, Colors.dark.surface]}
        style={styles.background}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.formContainer}>
            {renderForm()}
          </View>
        </ScrollView>
      </LinearGradient>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  logoBackground: {
    width: 80,
    height: 80,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  logoImage: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.dark.primary,
  },
  formContainer: {
    backgroundColor: Colors.dark.surface,
    borderRadius: 24,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
    borderWidth: 1,
    borderColor: 'rgba(226, 177, 60, 0.3)',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.dark.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.dark.textSecondary,
    marginBottom: 24,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(248, 250, 252, 0.05)',
    borderRadius: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
    height: 56,
    borderWidth: 1,
    borderColor: 'rgba(248, 250, 252, 0.1)',
  },
  input: {
    flex: 1,
    color: Colors.dark.text,
    fontSize: 16,
    marginLeft: 12,
    height: '100%',
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    color: Colors.dark.primary,
    fontSize: 14,
  },
  submitButton: {
    backgroundColor: Colors.dark.primary,
    borderRadius: 12,
    height: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  submitButtonText: {
    color: Colors.dark.background,
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(248, 250, 252, 0.1)',
  },
  dividerText: {
    color: Colors.dark.textSecondary,
    paddingHorizontal: 16,
    fontSize: 14,
  },
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  toggleText: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    marginRight: 4,
  },
  toggleButton: {
    color: Colors.dark.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  backButton: {
    alignItems: 'center',
    marginTop: 16,
  },
  backButtonText: {
    color: Colors.dark.primary,
    fontSize: 14,
    fontWeight: '600',
  },
});
