import React from 'react';
import { View, Text, StyleSheet, Image, Pressable } from 'react-native';
import { LogOut } from 'lucide-react-native';
import { useUserStore } from '@/stores/user-store';
import Colors from '@/constants/colors';

export default function ProfileHeader() {
  const { user, logout } = useUserStore();
  
  if (!user) return null;
  
  return (
    <View style={styles.container}>
      <View style={styles.profileInfo}>
        <Image 
          source={{ uri: user.photoUrl }} 
          style={styles.avatar} 
        />
        <View style={styles.textContainer}>
          <Text style={styles.name}>{user.displayName || user.email}</Text>
        </View>
      </View>
      
      <Pressable style={styles.logoutButton} onPress={logout}>
        <LogOut size={20} color={Colors.dark.background} />
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: Colors.dark.background,
  },
  textContainer: {
    marginLeft: 16,
  },
  name: {
    color: Colors.dark.background,
    fontSize: 18,
    fontWeight: '600',
  },
  email: {
    color: 'rgba(15, 23, 42, 0.8)',
    fontSize: 14,
    marginTop: 2,
  },
  logoutButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(15, 23, 42, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
