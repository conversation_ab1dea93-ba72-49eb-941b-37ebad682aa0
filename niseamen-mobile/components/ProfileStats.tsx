import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { Music } from 'lucide-react-native';
import { useUserStore } from '@/stores/user-store';
import { usePlayerStore } from '@/stores/player-store';
import Colors from '@/constants/colors';
import { LinearGradient } from 'expo-linear-gradient';

export default function ProfileStats() {
  const { user } = useUserStore();
  const { queue } = usePlayerStore();
  
  if (!user) return null;
  
  // Find last played song
  const lastPlayedSong = user.lastPlayedSongId 
    ? queue.find(song => song.id === user.lastPlayedSongId)
    : null;
  
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[Colors.dark.surface, 'rgba(30, 41, 59, 0.8)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.statCard}
      >
        <View style={styles.userInfoContainer}>
          <Image 
            source={{ uri: user.photoUrl }} 
            style={styles.userPhoto} 
          />
          <View style={styles.userTextContainer}>
            <Text style={styles.userName}>{user.displayName}</Text>
            <Text style={styles.userEmail}>{user.email}</Text>
          </View>
        </View>
        
        <View style={styles.divider} />
        
        <View style={styles.lastPlayedContainer}>
          <View style={styles.iconContainer}>
            <Music size={20} color={Colors.dark.primary} />
          </View>
          <View style={styles.lastPlayedTextContainer}>
            <Text style={styles.statLabel}>Last Played</Text>
            <Text style={styles.statValue} numberOfLines={1}>
              {lastPlayedSong ? lastPlayedSong.title : 'None'}
            </Text>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    marginTop: -20,
  },
  statCard: {
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(226, 177, 60, 0.3)',
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  userPhoto: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: Colors.dark.primary,
  },
  userTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  userName: {
    color: Colors.dark.text,
    fontSize: 16,
    fontWeight: '600',
  },
  userEmail: {
    color: Colors.dark.textSecondary,
    fontSize: 12,
    marginTop: 2,
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(226, 177, 60, 0.2)',
    marginBottom: 16,
  },
  lastPlayedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(226, 177, 60, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  lastPlayedTextContainer: {
    flex: 1,
  },
  statLabel: {
    color: Colors.dark.textSecondary,
    fontSize: 14,
    marginBottom: 4,
  },
  statValue: {
    color: Colors.dark.text,
    fontSize: 16,
    fontWeight: '600',
  },
});