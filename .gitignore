# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Environment variables and sensitive data
.env
.env.local
.env.production
.env.development
*.env

# Firebase configuration files (should be regenerated for each project)
android/app/google-services.json
ios/Runner/GoogleService-Info.plist
macos/Runner/GoogleService-Info.plist
web/firebase-config.js

# API keys and secrets
lib/config/api_config.dart
lib/config/api_keys.dart
lib/config/secrets.dart

# Real credentials file (NEVER commit this!)
REAL_CREDENTIALS.md

# Local configuration files
config/local.dart
config/development.dart
config/production.dart

# Generated files that may contain sensitive data
lib/firebase_options.dart.backup
android/key.properties
ios/Runner/Info.plist.backup

# APK files (don't commit built APKs)
*.apk
*.aab
*.ipa

# Keystore files
*.keystore
*.jks
android/app/upload-keystore.jks
android/app/release-keystore.jks

# Local scripts with sensitive data
scripts/deploy.sh
scripts/build-release.sh
niseamen_mobile/