import 'package:flutter/material.dart';

/// Modern icon collection for NiseAmen app
class AppIcons {
  // Navigation Icons
  static const IconData home = Icons.home_outlined;
  static const IconData homeFilled = Icons.home;
  static const IconData search = Icons.search_outlined;
  static const IconData searchFilled = Icons.search;
  static const IconData chat = Icons.chat_bubble_outline;
  static const IconData chatFilled = Icons.chat_bubble;
  static const IconData profile = Icons.person_outline;
  static const IconData profileFilled = Icons.person;

  // Media Player Icons
  static const IconData play = Icons.play_arrow_rounded;
  static const IconData pause = Icons.pause_rounded;
  static const IconData stop = Icons.stop_rounded;
  static const IconData skipNext = Icons.skip_next_rounded;
  static const IconData skipPrevious = Icons.skip_previous_rounded;
  static const IconData shuffle = Icons.shuffle_rounded;
  static const IconData repeat = Icons.repeat_rounded;
  static const IconData repeatOne = Icons.repeat_one_rounded;
  static const IconData volumeUp = Icons.volume_up_rounded;
  static const IconData volumeDown = Icons.volume_down_rounded;
  static const IconData volumeMute = Icons.volume_mute_rounded;

  // Content Icons
  static const IconData music = Icons.music_note_outlined;
  static const IconData musicFilled = Icons.music_note;
  static const IconData album = Icons.album_outlined;
  static const IconData albumFilled = Icons.album;
  static const IconData library = Icons.library_music_outlined;
  static const IconData libraryFilled = Icons.library_music;
  static const IconData playlist = Icons.playlist_play_outlined;
  static const IconData playlistFilled = Icons.playlist_play;

  // Interaction Icons
  static const IconData favorite = Icons.favorite_outline;
  static const IconData favoriteFilled = Icons.favorite;
  static const IconData share = Icons.share_outlined;
  static const IconData download = Icons.download_outlined;
  static const IconData downloadFilled = Icons.download;
  static const IconData bookmark = Icons.bookmark_outline;
  static const IconData bookmarkFilled = Icons.bookmark;

  // System Icons
  static const IconData settings = Icons.settings_outlined;
  static const IconData settingsFilled = Icons.settings;
  static const IconData notification = Icons.notifications_outlined;
  static const IconData notificationFilled = Icons.notifications;
  static const IconData theme = Icons.palette_outlined;
  static const IconData themeFilled = Icons.palette;

  // Action Icons
  static const IconData add = Icons.add_rounded;
  static const IconData remove = Icons.remove_rounded;
  static const IconData close = Icons.close_rounded;
  static const IconData check = Icons.check_rounded;
  static const IconData arrowBack = Icons.arrow_back_ios_rounded;
  static const IconData arrowForward = Icons.arrow_forward_ios_rounded;
  static const IconData expandMore = Icons.expand_more_rounded;
  static const IconData expandLess = Icons.expand_less_rounded;

  // Status Icons
  static const IconData loading = Icons.refresh_rounded;
  static const IconData error = Icons.error_outline;
  static const IconData warning = Icons.warning_outlined;
  static const IconData info = Icons.info_outlined;
  static const IconData success = Icons.check_circle_outline;

  // Religious Icons
  static const IconData church = Icons.church_outlined;
  static const IconData cross = Icons.add_circle_outline;
  static const IconData book = Icons.menu_book_outlined;
  static const IconData bookFilled = Icons.menu_book;
  static const IconData pray = Icons.volunteer_activism_outlined;
  static const IconData prayFilled = Icons.volunteer_activism;

  // Feature Icons
  static const IconData calendar = Icons.calendar_today_outlined;
  static const IconData calendarFilled = Icons.calendar_today;
  static const IconData clock = Icons.access_time_outlined;
  static const IconData clockFilled = Icons.access_time;
  static const IconData history = Icons.history_outlined;
  static const IconData historyFilled = Icons.history;
  static const IconData language = Icons.language_outlined;
  static const IconData languageFilled = Icons.language;
  static const IconData filter = Icons.filter_list_outlined;
  static const IconData sort = Icons.sort_outlined;

  // UI Elements
  static const IconData grid = Icons.grid_view_outlined;
  static const IconData gridFilled = Icons.grid_view;
  static const IconData list = Icons.view_list_outlined;
  static const IconData listFilled = Icons.view_list;
  static const IconData card = Icons.view_agenda_outlined;
  static const IconData cardFilled = Icons.view_agenda;

  // Helper method to get outlined/filled variants
  static IconData getVariant(IconData icon, bool filled) {
    final variants = <IconData, IconData>{
      home: homeFilled,
      search: searchFilled,
      chat: chatFilled,
      profile: profileFilled,
      music: musicFilled,
      album: albumFilled,
      library: libraryFilled,
      playlist: playlistFilled,
      favorite: favoriteFilled,
      download: downloadFilled,
      bookmark: bookmarkFilled,
      settings: settingsFilled,
      notification: notificationFilled,
      theme: themeFilled,
      book: bookFilled,
      pray: prayFilled,
      calendar: calendarFilled,
      clock: clockFilled,
       history: historyFilled,
      language: languageFilled,
      grid: gridFilled,
      list: listFilled,
      card: cardFilled,
    };

    if (filled && variants.containsKey(icon)) {
      return variants[icon]!;
    }
    return icon;
  }
}