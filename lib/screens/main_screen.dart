import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_icons.dart';
import '../constants/app_colors.dart';
import '../providers/simple_navigation_provider.dart';
import '../providers/audio_player_provider.dart';
import '../screens/home/<USER>';
import '../screens/search/search_screen.dart';
import '../screens/chat_screen.dart';
import '../screens/profile/profile_screen.dart';
import '../widgets/player/mini_player.dart';

class MainScreen extends ConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(navigationProvider);
    final audioState = ref.watch(audioPlayerProvider);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
      extendBody: true,
      body: Stack(
        children: [
          // Main content with proper padding for mini player
          Positioned.fill(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: audioState.currentSong != null ? 140 : 90,
              ),
              child: IndexedStack(
                index: currentIndex,
                children: const [
                  HomeScreen(),
                  SearchScreen(),
                  ChatScreen(),
                  ProfileScreen(),
                ],
              ),
            ),
          ),
          
          // Modern mini player with blur effect
          if (audioState.currentSong != null)
            Positioned(
              left: 0,
              right: 0,
              bottom: 90,
              child: const MiniPlayer(),
            ),
        ],
      ),
      bottomNavigationBar: _buildModernBottomNav(context, ref),
    );
  }

  Widget _buildModernBottomNav(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(navigationProvider);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      height: 90,
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        border: Border(
          top: BorderSide(
            color: isDark ? AppColors.neutral800 : AppColors.neutral200,
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(
              context: context,
              ref: ref,
              index: 0,
              icon: AppIcons.home,
              filledIcon: AppIcons.homeFilled,
              label: 'Home',
              isSelected: currentIndex == 0,
            ),
            _buildNavItem(
              context: context,
              ref: ref,
              index: 1,
              icon: AppIcons.search,
              filledIcon: AppIcons.searchFilled,
              label: 'Search',
              isSelected: currentIndex == 1,
            ),
            _buildNavItem(
              context: context,
              ref: ref,
              index: 2,
              icon: AppIcons.chat,
              filledIcon: AppIcons.chatFilled,
              label: 'Assistant',
              isSelected: currentIndex == 2,
            ),
            _buildNavItem(
              context: context,
              ref: ref,
              index: 3,
              icon: AppIcons.profile,
              filledIcon: AppIcons.profileFilled,
              label: 'Profile',
              isSelected: currentIndex == 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required WidgetRef ref,
    required int index,
    required IconData icon,
    required IconData filledIcon,
    required String label,
    required bool isSelected,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        ref.read(navigationProvider.notifier).setIndex(index);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.all(8),
              child: Icon(
                isSelected ? filledIcon : icon,
                size: 24,
                color: isSelected
                    ? (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue)
                    : (isDark ? AppColors.textTertiaryDark : AppColors.textTertiaryLight),
              ),
            ),
            const SizedBox(height: 4),
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 200),
              style: TextStyle(
                fontSize: 11,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected
                    ? (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue)
                    : (isDark ? AppColors.textTertiaryDark : AppColors.textTertiaryLight),
              ),
              child: Text(label),
            ),
          ],
        ),
      ),
    );
  }
}