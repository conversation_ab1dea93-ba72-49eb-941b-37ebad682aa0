import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:just_audio/just_audio.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_icons.dart';
import '../../providers/audio_player_provider.dart';
import '../../widgets/player/progress_bar.dart';
import '../../widgets/player/player_controls.dart';
import '../../widgets/player/language_selector.dart';
import 'full_screen_lyrics.dart';

class PlayerScreen extends ConsumerWidget {
  const PlayerScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final audioState = ref.watch(audioPlayerProvider);
    final notifier = ref.read(audioPlayerProvider.notifier);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    if (audioState.currentSong == null) {
      return Scaffold(
        backgroundColor: isDark ? AppColors.backgroundDark : AppColors.backgroundLight,
        body: Center(
          child: Text(
            'No song selected',
            style: TextStyle(
              color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: isDark ? AppColors.backgroundDark : AppColors.backgroundLight,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Now Playing',
          style: TextStyle(
            color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Song number
              _buildSongNumber(audioState, isDark),
              const SizedBox(height: 24),
              
              // Album art with rotation animation
              _buildAlbumArt(audioState, isDark),
              const SizedBox(height: 32),
              
              // Song info
              _buildSongInfo(audioState, isDark),
              const SizedBox(height: 24),
              
              // Progress bar
              ProgressBar(),
              const SizedBox(height: 24),
              
              // Player controls
              PlayerControls(),
              const SizedBox(height: 32),
              
              // Lyrics section
              _buildLyricsSection(context, audioState, notifier, isDark),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSongNumber(AudioPlayerState audioState, bool isDark) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isDark 
            ? AppColors.primaryBlue.withOpacity(0.2)
            : AppColors.primaryBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        '#${audioState.currentSong?.songNumber ?? ''}',
        style: TextStyle(
          color: isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue,
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildAlbumArt(AudioPlayerState audioState, bool isDark) {
    return Container(
      width: 280,
      height: 280,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDark ? Colors.black54 : Colors.black12,
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: audioState.currentSong!.imageUrl != null
            ? CachedNetworkImage(
                imageUrl: audioState.currentSong!.imageUrl!,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: isDark ? AppColors.neutral800 : AppColors.neutral200,
                  child: Icon(
                    AppIcons.music,
                    color: isDark ? AppColors.neutral600 : AppColors.neutral400,
                    size: 48,
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: isDark ? AppColors.neutral800 : AppColors.neutral200,
                  child: Icon(
                    AppIcons.music,
                    color: isDark ? AppColors.neutral600 : AppColors.neutral400,
                    size: 48,
                  ),
                ),
              )
            : Container(
                color: isDark ? AppColors.neutral800 : AppColors.neutral200,
                child: Icon(
                  AppIcons.music,
                  color: isDark ? AppColors.neutral600 : AppColors.neutral400,
                  size: 48,
                ),
              ),
      ),
    );
  }

  Widget _buildSongInfo(AudioPlayerState audioState, bool isDark) {
    return Column(
      children: [
        Text(
          audioState.currentSong!.title,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        Text(
          audioState.currentSong!.artist,
          style: TextStyle(
            fontSize: 16,
            color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildLyricsSection(BuildContext context, AudioPlayerState audioState, AudioPlayerNotifier notifier, bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark ? AppColors.borderDark : AppColors.borderLight,
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Lyrics header with language selector
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Lyrics',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
                ),
              ),
              LanguageSelector(),
            ],
          ),
          const SizedBox(height: 16),
          
          // Lyrics content
          if (audioState.hasLyrics)
            Text(
              audioState.currentLyrics,
              style: TextStyle(
                fontSize: 16,
                height: 1.5,
                color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
              ),
              textAlign: TextAlign.center,
            )
          else
            Text(
              'No lyrics available in ${audioState.selectedLanguage}',
              style: TextStyle(
                fontSize: 14,
                color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          
          const SizedBox(height: 16),
          
          // Full screen lyrics button
          if (audioState.hasLyrics)
            Center(
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const FullScreenLyricsScreen(),
                    ),
                  );
                },
                icon: Icon(
                  Icons.fullscreen,
                  size: 20,
                  color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
                ),
                label: Text(
                  'Full Screen Lyrics',
                  style: TextStyle(
                    color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: isDark 
                      ? AppColors.primaryBlue.withOpacity(0.2)
                      : AppColors.primaryBlue.withOpacity(0.1),
                  foregroundColor: isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
