import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Simple navigation provider for bottom navigation bar
class NavigationNotifier extends StateNotifier<int> {
  NavigationNotifier() : super(0);

  void setIndex(int index) {
    state = index;
  }
}

/// Navigation provider for the app
final navigationProvider = StateNotifierProvider<NavigationNotifier, int>(
  (ref) => NavigationNotifier(),
);