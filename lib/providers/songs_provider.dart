import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/song_model.dart';
import '../services/program_service.dart';
import '../services/firestore_adapter_service.dart';

/// Provider for managing songs state
class SongsNotifier extends StateNotifier<AsyncValue<List<Song>>> {
  SongsNotifier() : super(const AsyncValue.loading()) {
    loadSongs();
  }

  final ProgramService _programService = ProgramService.instance;

  Future<void> loadSongs({bool forceRefresh = false}) async {
    try {
      state = const AsyncValue.loading();
      
      // Test database structure to understand what collections exist
      debugPrint('🔍 Testing database structure...');
      final dbTest = await FirestoreAdapterService.instance.testDatabaseStructure();
      debugPrint('🗃️ Database test results: $dbTest');
      
      final songs = await _programService.getAllSongs(forceRefresh: forceRefresh);
      state = AsyncValue.data(songs);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> refreshSongs() async {
    await loadSongs(forceRefresh: true);
  }

  List<Song> searchSongs(String query) {
    return state.when(
      data: (songs) => songs.where((song) {
        final lowerQuery = query.toLowerCase();
        return song.title.toLowerCase().contains(lowerQuery) ||
               song.artist.toLowerCase().contains(lowerQuery) ||
               (song.songNumber?.toString().contains(query) ?? false);
      }).toList(),
      loading: () => [],
      error: (_, __) => [],
    );
  }

  Song? getSongById(String id) {
    return state.when(
      data: (songs) {
        try {
          return songs.firstWhere((song) => song.id == id);
        } catch (e) {
          return null;
        }
      },
      loading: () => null,
      error: (_, __) => null,
    );
  }
}

final songsProvider = StateNotifierProvider<SongsNotifier, AsyncValue<List<Song>>>((ref) {
  return SongsNotifier();
});

final songSearchProvider = StateProvider<String>((ref) => '');

final filteredSongsProvider = Provider<List<Song>>((ref) {
  final query = ref.watch(songSearchProvider);
  final songsAsync = ref.watch(songsProvider);
  
  return songsAsync.when(
    data: (songs) {
      if (query.isEmpty) return songs;
      
      final lowerQuery = query.toLowerCase();
      return songs.where((song) {
        return song.title.toLowerCase().contains(lowerQuery) ||
               song.artist.toLowerCase().contains(lowerQuery) ||
               (song.songNumber?.toString().contains(query) ?? false);
      }).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});