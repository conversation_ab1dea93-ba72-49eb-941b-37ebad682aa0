import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:just_audio/just_audio.dart';
import '../models/song_model.dart';
import '../services/audio_player_service.dart';

class AudioPlayerState {
  final Song? currentSong;
  final bool isPlaying;
  final Duration position;
  final Duration? duration;
  final bool shuffleMode;
  final LoopMode loopMode;
  final ProcessingState processingState;
  final String selectedLanguage;
  final bool isLyricsFullScreen;

  const AudioPlayerState({
    this.currentSong,
    required this.isPlaying,
    required this.position,
    this.duration,
    required this.shuffleMode,
    required this.loopMode,
    required this.processingState,
    this.selectedLanguage = 'goun',
    this.isLyricsFullScreen = false,
  });

  factory AudioPlayerState.initial() {
    return const AudioPlayerState(
      currentSong: null,
      isPlaying: false,
      position: Duration.zero,
      duration: null,
      shuffleMode: false,
      loopMode: LoopMode.off,
      processingState: ProcessingState.idle,
      selectedLanguage: 'goun',
      isLyricsFullScreen: false,
    );
  }

  AudioPlayerState copyWith({
    Song? currentSong,
    bool? isPlaying,
    Duration? position,
    Duration? duration,
    bool? shuffleMode,
    LoopMode? loopMode,
    ProcessingState? processingState,
    String? selectedLanguage,
    bool? isLyricsFullScreen,
  }) {
    return AudioPlayerState(
      currentSong: currentSong ?? this.currentSong,
      isPlaying: isPlaying ?? this.isPlaying,
      position: position ?? this.position,
      duration: duration ?? this.duration,
      shuffleMode: shuffleMode ?? this.shuffleMode,
      loopMode: loopMode ?? this.loopMode,
      processingState: processingState ?? this.processingState,
      selectedLanguage: selectedLanguage ?? this.selectedLanguage,
      isLyricsFullScreen: isLyricsFullScreen ?? this.isLyricsFullScreen,
    );
  }

  bool get isLoading => processingState == ProcessingState.loading;
  bool get isBuffering => processingState == ProcessingState.buffering;
  bool get hasError => processingState == ProcessingState.idle && currentSong != null && !isPlaying;
  
  double get progress {
    if (duration == null || duration!.inMilliseconds == 0) return 0.0;
    return position.inMilliseconds / duration!.inMilliseconds;
  }

  String get formattedPosition {
    final minutes = position.inMinutes;
    final seconds = position.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String get formattedDuration {
    if (duration == null) return '--:--';
    final minutes = duration!.inMinutes;
    final seconds = duration!.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  // Get current lyrics for selected language
  String get currentLyrics {
    if (currentSong == null) return '';
    return currentSong!.getLyrics(selectedLanguage);
  }

  // Check if lyrics are available for current language
  bool get hasLyrics {
    if (currentSong == null) return false;
    return currentSong!.hasLyrics(selectedLanguage);
  }

  // Get available languages for current song
  List<String> get availableLanguages {
    if (currentSong == null) return ['goun'];
    return currentSong!.availableLanguages;
  }
}

/// Provider for managing audio player state
class AudioPlayerNotifier extends StateNotifier<AudioPlayerState> {
  AudioPlayerNotifier() : super(AudioPlayerState.initial()) {
    _initialize();
  }

  final AudioPlayerService _audioService = AudioPlayerService.instance;

  void _initialize() {
    // Listen to player state changes
    _audioService.playerStateStream.listen((playerState) {
      state = state.copyWith(
        isPlaying: playerState.playing,
        processingState: playerState.processingState,
      );
    });

    // Listen to current song changes
    _audioService.currentSongStream.listen((song) {
      state = state.copyWith(currentSong: song);
    });

    // Listen to position changes
    _audioService.positionStream.listen((position) {
      state = state.copyWith(position: position);
    });

    // Listen to duration changes
    _audioService.durationStream.listen((duration) {
      state = state.copyWith(duration: duration);
    });

    // Listen to shuffle mode changes
    _audioService.shuffleModeStream.listen((shuffle) {
      state = state.copyWith(shuffleMode: shuffle);
    });

    // Listen to loop mode changes
    _audioService.loopModeStream.listen((loopMode) {
      state = state.copyWith(loopMode: loopMode);
    });
  }

  Future<void> playSong(Song song) async {
    try {
      await _audioService.playSong(song);
    } catch (e) {
      debugPrint('Error playing song: $e');
    }
  }

  Future<void> playPlaylist(List<Song> songs, {int startIndex = 0}) async {
    try {
      await _audioService.playPlaylist(songs, startIndex: startIndex);
    } catch (e) {
      debugPrint('Error playing playlist: $e');
    }
  }

  Future<void> play() async {
    await _audioService.resume();
  }

  Future<void> pause() async {
    await _audioService.pause();
  }

  Future<void> stop() async {
    await _audioService.stop();
  }

  Future<void> skipToNext() async {
    await _audioService.playNext();
  }

  Future<void> skipToPrevious() async {
    await _audioService.playPrevious();
  }

  Future<void> seek(Duration position) async {
    await _audioService.seek(position);
  }

  Future<void> toggleShuffle() async {
    await _audioService.setShuffle(!state.shuffleMode);
  }

  Future<void> toggleRepeat() async {
    final nextMode = switch (state.loopMode) {
      LoopMode.off => LoopMode.all,
      LoopMode.all => LoopMode.one,
      LoopMode.one => LoopMode.off,
    };
    await _audioService.setLoopMode(nextMode);
  }

  Future<void> setVolume(double volume) async {
    await _audioService.setVolume(volume);
  }

  // Add methods for language and lyrics management
  void setSelectedLanguage(String language) {
    state = state.copyWith(selectedLanguage: language);
  }

  void toggleLyricsFullScreen() {
    state = state.copyWith(isLyricsFullScreen: !state.isLyricsFullScreen);
  }
}

final audioPlayerProvider = StateNotifierProvider<AudioPlayerNotifier, AudioPlayerState>((ref) {
  return AudioPlayerNotifier();
});
