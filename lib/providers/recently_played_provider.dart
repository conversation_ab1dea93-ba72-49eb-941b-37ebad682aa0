import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/song_model.dart';
import '../services/firestore_service.dart';
import 'auth_provider.dart';
import 'chat_provider.dart';

/// Provider for recently played songs
final recentlyPlayedProvider = StreamProvider<List<Song>>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final currentUser = ref.watch(currentUserProvider);
  
  if (currentUser == null) {
    return Stream.value([]);
  }

  return firestoreService.firestore
      .collection('users')
      .doc(currentUser.uid)
      .snapshots()
      .asyncMap((snapshot) async {
    if (!snapshot.exists) {
      return [];
    }

    final userData = snapshot.data();
    final recentlyPlayedIds = (userData?['recentlyPlayed'] as List<dynamic>?)?.cast<String>() ?? [];
    
    if (recentlyPlayedIds.isEmpty) {
      return [];
    }

    // Fetch the actual song data for each ID
    final songs = <Song>[];
    for (final songId in recentlyPlayedIds.take(10)) { // Limit to last 10 songs
      try {
        final songDoc = await firestoreService.firestore
            .collection('songs')
            .doc(songId)
            .get();
        
        if (songDoc.exists) {
          final songData = songDoc.data();
          if (songData != null) {
            songs.add(Song.fromJson(songData));
          }
        }
      } catch (e) {
        // Skip songs that can't be loaded
        continue;
      }
    }

    return songs;
  });
});

/// Notifier for managing recently played songs
class RecentlyPlayedNotifier extends StateNotifier<List<Song>> {
  RecentlyPlayedNotifier() : super([]);

  /// Add a song to recently played
  void addSong(Song song) {
    // Remove if already exists to avoid duplicates
    final newState = state.where((s) => s.id != song.id).toList();
    // Add to beginning of list
    newState.insert(0, song);
    // Limit to last 20 songs
    state = newState.take(20).toList();
  }

  /// Clear recently played list
  void clear() {
    state = [];
  }
}

/// Provider for RecentlyPlayedNotifier
final recentlyPlayedNotifierProvider = StateNotifierProvider<RecentlyPlayedNotifier, List<Song>>((ref) {
  return RecentlyPlayedNotifier();
});
