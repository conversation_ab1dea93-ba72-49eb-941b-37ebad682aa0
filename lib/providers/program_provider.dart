import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/week_program_model.dart';
import '../models/song_model.dart';
import '../services/program_service.dart';

final programServiceProvider = Provider<ProgramService>((ref) {
  return ProgramService.instance;
});

// Current week program provider
final currentWeekProvider = FutureProvider<WeekProgramModel?>((ref) async {
  final programService = ref.watch(programServiceProvider);
  return await programService.getCurrentWeekProgram();
});

// Current month program provider
final currentMonthProvider = FutureProvider<MonthProgramModel?>((ref) async {
  final programService = ref.watch(programServiceProvider);
  return await programService.fetchCurrentMonthProgram();
});

// Featured song provider
final featuredSongProvider = FutureProvider<int?>((ref) async {
  final programService = ref.watch(programServiceProvider);
  return await programService.getTodaysFeaturedSong();
});

// Program loading state
final programLoadingProvider = StateProvider<bool>((ref) => false);

// Program error state
final programErrorProvider = StateProvider<String?>((ref) => null);