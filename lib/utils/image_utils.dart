import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class ImageUtils {
  /// Get appropriate image provider for song album art
  static ImageProvider getImageProvider(String? imageUrl) {
    if (imageUrl != null && imageUrl.isNotEmpty && imageUrl.startsWith('http')) {
      return NetworkImage(imageUrl);
    }
    return const AssetImage(AppConstants.defaultAlbumArt);
  }

  /// Get album art widget with proper fallback
  static Widget buildAlbumArt({
    String? imageUrl,
    required double width,
    required double height,
    double borderRadius = 8.0,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        image: DecorationImage(
          image: getImageProvider(imageUrl),
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}