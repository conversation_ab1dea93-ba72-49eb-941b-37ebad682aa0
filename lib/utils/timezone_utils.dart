import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import '../constants/app_constants.dart';

/// Timezone utilities for NiseAmen app
/// All dates and times are calculated in GMT+1 (Africa/Lagos)
class TimezoneUtils {
  static late tz.Location _location;
  static bool _initialized = false;

  /// Initialize timezone data
  static Future<void> initialize() async {
    if (_initialized) return;
    
    tz.initializeTimeZones();
    _location = tz.getLocation(AppConstants.primaryTimezone);
    _initialized = true;
  }

  /// Get current date and time in GMT+1
  static DateTime getCurrentGMTPlus1() {
    _ensureInitialized();
    return tz.TZDateTime.now(_location);
  }

  /// Convert any DateTime to GMT+1
  static DateTime toGMTPlus1(DateTime dateTime) {
    _ensureInitialized();
    return tz.TZDateTime.from(dateTime, _location);
  }

  /// Get the start of today in GMT+1 (midnight)
  static DateTime getTodayStart() {
    final now = getCurrentGMTPlus1();
    return DateTime(now.year, now.month, now.day);
  }

  /// Get the end of today in GMT+1 (23:59:59.999)
  static DateTime getTodayEnd() {
    final now = getCurrentGMTPlus1();
    return DateTime(now.year, now.month, now.day, 23, 59, 59, 999);
  }

  /// Get day of week index for ISO weeks (Monday = 0, Sunday = 6)
  /// This is different from Dart's default where Monday = 1, Sunday = 7
  static int getDayOfWeekIndex(DateTime date) {
    final gmtPlus1Date = toGMTPlus1(date);
    // Convert from Dart's weekday (1=Monday, 7=Sunday) to our index (0=Monday, 6=Sunday)
    return gmtPlus1Date.weekday == 7 ? 6 : gmtPlus1Date.weekday - 1;
  }

  /// Get ISO week number for a given date
  static int getISOWeekNumber(DateTime date) {
    final gmtPlus1Date = toGMTPlus1(date);
    
    // ISO week calculation
    final jan4 = DateTime(gmtPlus1Date.year, 1, 4);
    final jan4Weekday = jan4.weekday;
    final weekOneStart = jan4.subtract(Duration(days: jan4Weekday - 1));
    
    final daysSinceWeekOne = gmtPlus1Date.difference(weekOneStart).inDays;
    return (daysSinceWeekOne / 7).ceil();
  }

  /// Get the Monday of the current week
  static DateTime getMondayOfWeek(DateTime date) {
    final gmtPlus1Date = toGMTPlus1(date);
    final daysFromMonday = gmtPlus1Date.weekday - 1;
    return gmtPlus1Date.subtract(Duration(days: daysFromMonday));
  }

  /// Get the Sunday of the current week
  static DateTime getSundayOfWeek(DateTime date) {
    final monday = getMondayOfWeek(date);
    return monday.add(const Duration(days: 6));
  }

  /// Check if two dates are on the same day in GMT+1
  static bool isSameDay(DateTime date1, DateTime date2) {
    final gmt1Date1 = toGMTPlus1(date1);
    final gmt1Date2 = toGMTPlus1(date2);
    
    return gmt1Date1.year == gmt1Date2.year &&
           gmt1Date1.month == gmt1Date2.month &&
           gmt1Date1.day == gmt1Date2.day;
  }

  /// Check if a date is today in GMT+1
  static bool isToday(DateTime date) {
    return isSameDay(date, getCurrentGMTPlus1());
  }

  /// Check if a date is yesterday in GMT+1
  static bool isYesterday(DateTime date) {
    final yesterday = getCurrentGMTPlus1().subtract(const Duration(days: 1));
    return isSameDay(date, yesterday);
  }

  /// Check if a date is tomorrow in GMT+1
  static bool isTomorrow(DateTime date) {
    final tomorrow = getCurrentGMTPlus1().add(const Duration(days: 1));
    return isSameDay(date, tomorrow);
  }

  /// Get the number of days between two dates in GMT+1
  static int daysBetween(DateTime date1, DateTime date2) {
    final gmt1Date1 = DateTime(
      toGMTPlus1(date1).year,
      toGMTPlus1(date1).month,
      toGMTPlus1(date1).day,
    );
    final gmt1Date2 = DateTime(
      toGMTPlus1(date2).year,
      toGMTPlus1(date2).month,
      toGMTPlus1(date2).day,
    );
    
    return gmt1Date2.difference(gmt1Date1).inDays;
  }

  /// Get formatted date string in GMT+1
  static String formatDate(DateTime date, {String pattern = 'dd-MM-yyyy'}) {
    final gmt1Date = toGMTPlus1(date);
    
    switch (pattern) {
      case 'dd-MM-yyyy':
        return '${gmt1Date.day.toString().padLeft(2, '0')}-'
               '${gmt1Date.month.toString().padLeft(2, '0')}-'
               '${gmt1Date.year}';
      case 'MM-dd-yyyy':
        return '${gmt1Date.month.toString().padLeft(2, '0')}-'
               '${gmt1Date.day.toString().padLeft(2, '0')}-'
               '${gmt1Date.year}';
      case 'yyyy-MM-dd':
        return '${gmt1Date.year}-'
               '${gmt1Date.month.toString().padLeft(2, '0')}-'
               '${gmt1Date.day.toString().padLeft(2, '0')}';
      default:
        return gmt1Date.toString();
    }
  }

  /// Get formatted time string in GMT+1
  static String formatTime(DateTime date, {bool use24Hour = true}) {
    final gmt1Date = toGMTPlus1(date);
    
    if (use24Hour) {
      return '${gmt1Date.hour.toString().padLeft(2, '0')}:'
             '${gmt1Date.minute.toString().padLeft(2, '0')}';
    } else {
      final hour = gmt1Date.hour;
      final hour12 = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
      final amPm = hour < 12 ? 'AM' : 'PM';
      return '${hour12.toString().padLeft(2, '0')}:'
             '${gmt1Date.minute.toString().padLeft(2, '0')} $amPm';
    }
  }

  /// Get relative time string (e.g., "2 hours ago", "in 3 days")
  static String getRelativeTime(DateTime date) {
    final now = getCurrentGMTPlus1();
    final gmt1Date = toGMTPlus1(date);
    final difference = gmt1Date.difference(now);
    
    final absDifference = difference.abs();
    final isPast = difference.isNegative;
    
    if (absDifference.inDays >= 1) {
      final days = absDifference.inDays;
      if (isPast) {
        return days == 1 ? 'Yesterday' : '$days days ago';
      } else {
        return days == 1 ? 'Tomorrow' : 'In $days days';
      }
    } else if (absDifference.inHours >= 1) {
      final hours = absDifference.inHours;
      if (isPast) {
        return hours == 1 ? '1 hour ago' : '$hours hours ago';
      } else {
        return hours == 1 ? 'In 1 hour' : 'In $hours hours';
      }
    } else if (absDifference.inMinutes >= 1) {
      final minutes = absDifference.inMinutes;
      if (isPast) {
        return minutes == 1 ? '1 minute ago' : '$minutes minutes ago';
      } else {
        return minutes == 1 ? 'In 1 minute' : 'In $minutes minutes';
      }
    } else {
      return 'Just now';
    }
  }

  /// Get week boundaries for a given date
  static ({DateTime start, DateTime end}) getWeekBoundaries(DateTime date) {
    final monday = getMondayOfWeek(date);
    final sunday = getSundayOfWeek(date);
    
    return (
      start: DateTime(monday.year, monday.month, monday.day),
      end: DateTime(sunday.year, sunday.month, sunday.day, 23, 59, 59, 999),
    );
  }

  /// Get month boundaries for a given date
  static ({DateTime start, DateTime end}) getMonthBoundaries(DateTime date) {
    final gmt1Date = toGMTPlus1(date);
    final start = DateTime(gmt1Date.year, gmt1Date.month, 1);
    final end = DateTime(gmt1Date.year, gmt1Date.month + 1, 0, 23, 59, 59, 999);
    
    return (start: start, end: end);
  }

  /// Calculate featured song rotation based on date
  /// This ensures the same song is featured for the entire day in GMT+1
  static int calculateDailyRotation(DateTime date, int totalSongs) {
    final gmt1Date = toGMTPlus1(date);
    final daysSinceEpoch = DateTime(gmt1Date.year, gmt1Date.month, gmt1Date.day)
        .difference(DateTime(2024, 1, 1))
        .inDays;
    
    return daysSinceEpoch % totalSongs;
  }

  /// Get the next midnight in GMT+1
  static DateTime getNextMidnight() {
    final now = getCurrentGMTPlus1();
    return DateTime(now.year, now.month, now.day + 1);
  }

  /// Get time until next midnight in GMT+1
  static Duration getTimeUntilMidnight() {
    final now = getCurrentGMTPlus1();
    final nextMidnight = getNextMidnight();
    return nextMidnight.difference(now);
  }

  /// Ensure timezone is initialized
  static void _ensureInitialized() {
    if (!_initialized) {
      throw StateError('TimezoneUtils not initialized. Call TimezoneUtils.initialize() first.');
    }
  }

  /// Get day name in English
  static String getDayName(DateTime date) {
    final gmt1Date = toGMTPlus1(date);
    const dayNames = [
      'Monday', 'Tuesday', 'Wednesday', 'Thursday', 
      'Friday', 'Saturday', 'Sunday'
    ];
    
    final dayIndex = gmt1Date.weekday == 7 ? 6 : gmt1Date.weekday - 1;
    return dayNames[dayIndex];
  }

  /// Get month name in English
  static String getMonthName(DateTime date) {
    final gmt1Date = toGMTPlus1(date);
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    return monthNames[gmt1Date.month - 1];
  }
}