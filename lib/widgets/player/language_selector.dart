import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/audio_player_provider.dart';
import '../../constants/app_colors.dart';

class LanguageSelector extends ConsumerWidget {
  final bool compact;

  const LanguageSelector({super.key, this.compact = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final audioState = ref.watch(audioPlayerProvider);
    final notifier = ref.read(audioPlayerProvider.notifier);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    if (audioState.currentSong == null || audioState.availableLanguages.length <= 1) {
      return const SizedBox.shrink();
    }

    return PopupMenuButton<String>(
      icon: Icon(
        Icons.language,
        color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
        size: compact ? 20 : 24,
      ),
      onSelected: (language) => notifier.setSelectedLanguage(language),
      itemBuilder: (BuildContext context) {
        return audioState.availableLanguages.map((language) {
          return PopupMenuItem<String>(
            value: language,
            child: Row(
              children: [
                if (language == audioState.selectedLanguage)
                  Icon(
                    Icons.check,
                    color: isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue,
                    size: 20,
                  )
                else
                  const SizedBox(width: 20),
                const SizedBox(width: 8),
                Text(
                  _getLanguageDisplayName(language),
                  style: TextStyle(
                    color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
                  ),
                ),
              ],
            ),
          );
        }).toList();
      },
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
    );
  }

  String _getLanguageDisplayName(String languageCode) {
    switch (languageCode) {
      case 'goun':
        return 'Goun';
      case 'french':
        return 'French';
      case 'english':
        return 'English';
      case 'yoruba':
        return 'Yoruba';
      case 'fon':
        return 'Fon';
      default:
        return languageCode;
    }
  }
}
