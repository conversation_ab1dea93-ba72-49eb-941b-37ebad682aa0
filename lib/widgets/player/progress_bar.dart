import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/audio_player_provider.dart';
import '../../constants/app_colors.dart';

class ProgressBar extends ConsumerWidget {
  const ProgressBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final audioState = ref.watch(audioPlayerProvider);
    final notifier = ref.read(audioPlayerProvider.notifier);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      children: [
        // Progress slider
        SliderTheme(
          data: SliderThemeData(
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
            activeTrackColor: isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue,
            inactiveTrackColor: isDark ? AppColors.neutral700 : AppColors.neutral300,
            thumbColor: isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue,
            overlayColor: (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue)
                .withOpacity(0.2),
          ),
          child: Slider(
            value: audioState.position.inSeconds.toDouble(),
            min: 0,
            max: audioState.duration?.inSeconds.toDouble() ?? 1,
            onChanged: (value) {
              notifier.seek(Duration(seconds: value.toInt()));
            },
          ),
        ),
        const SizedBox(height: 8),
        
        // Time labels
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                audioState.formattedPosition,
                style: TextStyle(
                  fontSize: 12,
                  color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
                ),
              ),
              Text(
                audioState.formattedDuration,
                style: TextStyle(
                  fontSize: 12,
                  color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
