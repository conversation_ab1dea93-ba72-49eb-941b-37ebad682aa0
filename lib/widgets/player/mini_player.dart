import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_icons.dart';
import '../../providers/audio_player_provider.dart';
import '../../screens/player/player_screen.dart';
import '../../utils/image_utils.dart';

class MiniPlayer extends ConsumerWidget {
  const MiniPlayer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final audioState = ref.watch(audioPlayerProvider);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    if (audioState.currentSong == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: isDark ? Colors.black54 : Colors.black12,
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            height: 64,
            decoration: BoxDecoration(
              color: isDark 
                  ? AppColors.cardDark.withOpacity(0.95)
                  : AppColors.cardLight.withOpacity(0.95),
              border: Border.all(
                color: isDark 
                    ? AppColors.cardDarkBorder.withOpacity(0.3)
                    : AppColors.cardLightBorder.withOpacity(0.3),
                width: 0.5,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _openFullPlayer(context),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Row(
                    children: [
                      // Album artwork with modern design
                      _buildAlbumArt(audioState, isDark),
                      const SizedBox(width: 12),
                      
                      // Song info
                      Expanded(
                        child: _buildSongInfo(audioState, isDark),
                      ),
                      
                      // Play controls
                      _buildPlayControls(ref, audioState, isDark),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAlbumArt(AudioPlayerState audioState, bool isDark) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: isDark ? Colors.black54 : Colors.black12,
            blurRadius: 8,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: audioState.currentSong!.imageUrl != null
            ? CachedNetworkImage(
                imageUrl: audioState.currentSong!.imageUrl!,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: isDark ? AppColors.neutral800 : AppColors.neutral200,
                  child: Icon(
                    AppIcons.music,
                    color: isDark ? AppColors.neutral600 : AppColors.neutral400,
                    size: 20,
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: isDark ? AppColors.neutral800 : AppColors.neutral200,
                  child: Icon(
                    AppIcons.music,
                    color: isDark ? AppColors.neutral600 : AppColors.neutral400,
                    size: 20,
                  ),
                ),
              )
            : Container(
                color: isDark ? AppColors.neutral800 : AppColors.neutral200,
                child: Icon(
                  AppIcons.music,
                  color: isDark ? AppColors.neutral600 : AppColors.neutral400,
                  size: 20,
                ),
              ),
      ),
    );
  }

  Widget _buildSongInfo(AudioPlayerState audioState, bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          audioState.currentSong!.title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 2),
        Text(
          audioState.currentSong!.artist,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildPlayControls(WidgetRef ref, AudioPlayerState audioState, bool isDark) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Heart button
        _buildControlButton(
          icon: AppIcons.favorite,
          onPressed: () {
            HapticFeedback.lightImpact();
            // TODO: Implement favorite functionality
          },
          isDark: isDark,
          size: 20,
        ),
        const SizedBox(width: 8),
        
        // Play/Pause button
        _buildControlButton(
          icon: audioState.isPlaying ? AppIcons.pause : AppIcons.play,
          onPressed: () {
            HapticFeedback.lightImpact();
            final notifier = ref.read(audioPlayerProvider.notifier);
            if (audioState.isPlaying) {
              notifier.pause();
            } else {
              notifier.play();
            }
          },
          isDark: isDark,
          size: 24,
          isPrimary: true,
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required bool isDark,
    required double size,
    bool isPrimary = false,
  }) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isPrimary
            ? (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue)
            : Colors.transparent,
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          size: size,
          color: isPrimary
              ? Colors.white
              : (isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight),
        ),
        padding: EdgeInsets.zero,
        splashRadius: 16,
      ),
    );
  }

  void _openFullPlayer(BuildContext context) {
    HapticFeedback.mediumImpact();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PlayerScreen(),
      ),
    );
  }
}

class FullPlayerScreen extends ConsumerWidget {
  const FullPlayerScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final audioState = ref.watch(audioPlayerProvider);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: isDark ? AppColors.neutral700 : AppColors.neutral300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    AppIcons.expandMore,
                    color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        'Now Playing',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
                        ),
                      ),
                      Text(
                        audioState.currentSong?.title ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () {
                    // TODO: Implement more options
                  },
                  icon: Icon(
                    Icons.more_vert,
                    color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
                  ),
                ),
              ],
            ),
          ),
          
          const Expanded(
            child: Center(
              child: Text('Full Player UI Coming Soon...'),
            ),
          ),
        ],
      ),
    );
  }
}
