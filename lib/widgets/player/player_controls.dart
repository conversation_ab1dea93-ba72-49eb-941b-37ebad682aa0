import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/services.dart';
import 'package:just_audio/just_audio.dart';
import '../../providers/audio_player_provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_icons.dart';

class PlayerControls extends ConsumerWidget {
  final String size;
  final bool minimal;

  const PlayerControls({
    super.key,
    this.size = 'medium',
    this.minimal = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final audioState = ref.watch(audioPlayerProvider);
    final notifier = ref.read(audioPlayerProvider.notifier);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    final iconSize = switch (size) {
      'small' => 20.0,
      'medium' => 24.0,
      'large' => 32.0,
      _ => 24.0,
    };

    final buttonSize = switch (size) {
      'small' => 36.0,
      'medium' => 44.0,
      'large' => 56.0,
      _ => 44.0,
    };

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (!minimal) ...[
          // Shuffle button
          _buildControlButton(
            icon: AppIcons.shuffle,
            isActive: audioState.shuffleMode,
            onPressed: () {
              HapticFeedback.lightImpact();
              notifier.toggleShuffle();
            },
            size: buttonSize,
            iconSize: iconSize,
            isDark: isDark,
          ),
          const SizedBox(width: 16),
          
          // Previous button
          _buildControlButton(
            icon: AppIcons.skipPrevious,
            onPressed: () {
              HapticFeedback.lightImpact();
              notifier.skipToPrevious();
            },
            size: buttonSize,
            iconSize: iconSize,
            isDark: isDark,
          ),
          const SizedBox(width: 16),
        ],
        
        // Play/Pause button (primary)
        Container(
          width: buttonSize * 1.4,
          height: buttonSize * 1.4,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue,
            boxShadow: [
              BoxShadow(
                color: (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue)
                    .withOpacity(0.3),
                blurRadius: 8,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            onPressed: () {
              HapticFeedback.mediumImpact();
              if (audioState.isPlaying) {
                notifier.pause();
              } else {
                notifier.play();
              }
            },
            icon: Icon(
              audioState.isPlaying ? AppIcons.pause : AppIcons.play,
              color: Colors.white,
              size: iconSize * 1.2,
            ),
            padding: EdgeInsets.zero,
            splashRadius: buttonSize * 0.7,
          ),
        ),
        
        if (!minimal) ...[
          const SizedBox(width: 16),
          
          // Next button
          _buildControlButton(
            icon: AppIcons.skipNext,
            onPressed: () {
              HapticFeedback.lightImpact();
              notifier.skipToNext();
            },
            size: buttonSize,
            iconSize: iconSize,
            isDark: isDark,
          ),
          const SizedBox(width: 16),
          
          // Repeat button
          _buildControlButton(
            icon: _getRepeatIcon(audioState.loopMode),
            isActive: audioState.loopMode != LoopMode.off,
            onPressed: () {
              HapticFeedback.lightImpact();
              notifier.toggleRepeat();
            },
            size: buttonSize,
            iconSize: iconSize,
            isDark: isDark,
          ),
        ],
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required bool isDark,
    required double size,
    required double iconSize,
    bool isActive = false,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isActive
            ? (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue)
            : Colors.transparent,
        border: Border.all(
          color: isActive
              ? Colors.transparent
              : (isDark ? AppColors.neutral700 : AppColors.neutral300),
          width: 1,
        ),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          size: iconSize,
          color: isActive
              ? Colors.white
              : (isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight),
        ),
        padding: EdgeInsets.zero,
        splashRadius: size * 0.7,
      ),
    );
  }

  IconData _getRepeatIcon(LoopMode loopMode) {
    return switch (loopMode) {
      LoopMode.off => AppIcons.repeat,
      LoopMode.all => AppIcons.repeat,
      LoopMode.one => AppIcons.repeatOne,
    };
  }
}
