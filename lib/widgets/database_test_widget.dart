import 'package:flutter/material.dart';
import '../services/program_service.dart';

/// Widget to test and display your existing database structure
class DatabaseTestWidget extends StatefulWidget {
  const DatabaseTestWidget({super.key});

  @override
  State<DatabaseTestWidget> createState() => _DatabaseTestWidgetState();
}

class _DatabaseTestWidgetState extends State<DatabaseTestWidget> {
  Map<String, dynamic>? _testResults;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _testDatabase();
  }

  Future<void> _testDatabase() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final results = await ProgramService.instance.testExistingDatabase();
      setState(() {
        _testResults = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _testResults = {'error': e.toString()};
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Test'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _testDatabase,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _testResults == null
              ? const Center(child: Text('No test results'))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSection('Database Structure Test', _testResults!),
                      const SizedBox(height: 20),
                      _buildTestSongsButton(),
                      const SizedBox(height: 20),
                      _buildTestProgramsButton(),
                    ],
                  ),
                ),
    );
  }

  Widget _buildSection(String title, Map<String, dynamic> data) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ...data.entries.map((entry) => _buildDataRow(entry.key, entry.value)),
          ],
        ),
      ),
    );
  }

  Widget _buildDataRow(String key, dynamic value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 200,
            child: Text(
              key,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: value is Map || value is List
                ? Text(value.toString())
                : Text(value.toString()),
          ),
        ],
      ),
    );
  }

  Widget _buildTestSongsButton() {
    return ElevatedButton(
      onPressed: () => _testSongs(),
      child: const Text('Test Load Songs'),
    );
  }

  Widget _buildTestProgramsButton() {
    return ElevatedButton(
      onPressed: () => _testPrograms(),
      child: const Text('Test Load Current Program'),
    );
  }

  Future<void> _testSongs() async {
    try {
      final songs = await ProgramService.instance.getAllSongs(forceRefresh: true);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully loaded ${songs.length} songs'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading songs: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _testPrograms() async {
    try {
      final program = await ProgramService.instance.getCurrentMonthProgram(forceRefresh: true);
      if (mounted) {
        final message = program != null
            ? 'Successfully loaded program for ${program.monthName} ${program.year}'
            : 'No program found for current month';
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: program != null ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading program: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}