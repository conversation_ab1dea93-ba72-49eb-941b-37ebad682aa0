import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_icons.dart';
import '../../models/week_program_model.dart';
import '../../models/song_model.dart';
import '../../providers/program_provider.dart';
import '../../services/program_service.dart';
import '../../providers/songs_provider.dart';

class ProgramCard extends ConsumerStatefulWidget {
  const ProgramCard({super.key});

  @override
  ConsumerState<ProgramCard> createState() => _ProgramCardState();
}

class _ProgramCardState extends ConsumerState<ProgramCard> {
  late ProgramService _programService;
  List<Song?> _weekSongs = [];
  bool _isLoadingSongs = false;

  @override
  void initState() {
    super.initState();
    _programService = ProgramService.instance;
  }

  @override
  Widget build(BuildContext context) {
    final weekAsync = ref.watch(currentWeekProvider);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return weekAsync.when(
      data: (week) => week != null
          ? _buildProgramContent(week, isDark)
          : _buildEmptyState(isDark),
      loading: () => _buildLoadingState(isDark),
      error: (error, _) => _buildErrorState(isDark),
    );
  }

  Widget _buildProgramContent(WeekProgramModel week, bool isDark) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: isDark ? AppColors.cardDark : AppColors.cardLight,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark ? AppColors.cardDarkBorder : AppColors.cardLightBorder,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isDark ? AppColors.primaryBlue.withOpacity(0.2) : AppColors.primaryBlue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    AppIcons.calendar,
                    size: 16,
                    color: isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _programService.formatWeekDate(week),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
                  ),
                ),
              ],
            ),
          ),

          // Days scroll
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: week.songs.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: _buildDayCard(week, index, isDark),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDayCard(WeekProgramModel week, int dayIndex, bool isDark) {
    final songNumber = week.songs[dayIndex];
    final dayName = _programService.formatDayOfWeek(dayIndex);
    final isToday = _isToday(dayIndex);
    final songsAsync = ref.watch(songsProvider);

    return Container(
      width: 100,
      decoration: BoxDecoration(
        color: isToday
            ? (isDark ? AppColors.primaryBlue.withOpacity(0.2) : AppColors.primaryBlue.withOpacity(0.1))
            : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isToday
              ? (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue)
              : Colors.transparent,
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _handleSongPress(songNumber),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Day name
                Text(
                  dayName,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: isToday
                        ? (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue)
                        : (isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight),
                  ),
                ),
                const SizedBox(height: 8),

                // Song number circle
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isToday
                        ? (isDark ? AppColors.primaryBlue : AppColors.primaryBlue)
                        : (isDark ? AppColors.neutral600 : AppColors.neutral200),
                  ),
                  child: Center(
                    child: Text(
                      songNumber.toString(),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: isToday
                            ? Colors.white
                            : (isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight),
                      ),
                    ),
                  ),
                ),

                if (isToday) ...[
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: isDark ? AppColors.primaryBlue : AppColors.primaryBlue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Today',
                      style: const TextStyle(
                        fontSize: 8,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState(bool isDark) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: isDark ? AppColors.cardDark : AppColors.cardLight,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark ? AppColors.cardDarkBorder : AppColors.cardLightBorder,
          width: 1,
        ),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildEmptyState(bool isDark) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: isDark ? AppColors.cardDark : AppColors.cardLight,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark ? AppColors.cardDarkBorder : AppColors.cardLightBorder,
          width: 1,
        ),
      ),
      child: const Center(
        child: Text('No weekly program available'),
      ),
    );
  }

  Widget _buildErrorState(bool isDark) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: isDark ? AppColors.cardDark : AppColors.cardLight,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.error,
          width: 1,
        ),
      ),
      child: const Center(
        child: Text('Error loading program'),
      ),
    );
  }

  bool _isToday(int dayIndex) {
    // Get current day of week (0 = Monday, 6 = Sunday)
    final now = DateTime.now();
    final currentDayOfWeek = now.weekday == DateTime.sunday ? 6 : now.weekday - 1;
    return currentDayOfWeek == dayIndex;
  }

  void _handleSongPress(int songNumber) {
    HapticFeedback.lightImpact();

    // Find the song in the songs list
    final songsAsync = ref.read(songsProvider);
    songsAsync.whenData((songs) {
      final song = songs?.cast<Song?>().firstWhere(
            (s) => s?.songNumber == songNumber,
            orElse: () => null,
          );

      if (song != null) {
        // Navigate to player or handle song selection
        debugPrint('Selected song: ${song.title}');
        // TODO: Navigate to player screen with the selected song
      }
    });
  }
}