import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_icons.dart';
import '../../models/song_model.dart';
import '../../providers/audio_player_provider.dart';

class FeaturedSongCard extends ConsumerWidget {
  final Song song;

  const FeaturedSongCard({super.key, required this.song});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final audioState = ref.watch(audioPlayerProvider);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final isCurrentSong = audioState.currentSong?.id == song.id;
    final isPlaying = isCurrentSong && audioState.isPlaying;

    return Container(
      height: 120,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDark
              ? [
                  AppColors.primaryBlueLight.withOpacity(0.8),
                  AppColors.secondaryPurpleLight.withOpacity(0.6),
                ]
              : [
                  AppColors.primaryBlue.withOpacity(0.8),
                  AppColors.secondaryPurple.withOpacity(0.6),
                ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue).withOpacity(0.3),
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _onTap(ref, audioState),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // Album artwork
                _buildAlbumArt(),
                
                const SizedBox(width: 16),
                
                // Song info
                Expanded(
                  child: _buildSongInfo(),
                ),
                
                // Play button
                _buildPlayButton(ref, audioState, isPlaying),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAlbumArt() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: song.imageUrl != null
            ? CachedNetworkImage(
                imageUrl: song.imageUrl!,
                fit: BoxFit.cover,
                placeholder: (context, url) => _buildPlaceholder(),
                errorWidget: (context, url, error) => _buildPlaceholder(),
              )
            : _buildPlaceholder(),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      color: Colors.white.withOpacity(0.2),
      child: const Icon(
        AppIcons.music,
        color: Colors.white,
        size: 32,
      ),
    );
  }

  Widget _buildSongInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Text(
            'FEATURED',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w700,
              color: Colors.white,
              letterSpacing: 1,
            ),
          ),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          song.title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        
        const SizedBox(height: 4),
        
        Row(
          children: [
            if (song.songNumber != null) ...[
              Text(
                'Hymn #${song.songNumber}',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                width: 2,
                height: 2,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
            ],
            Expanded(
              child: Text(
                song.artist,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white.withOpacity(0.8),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPlayButton(WidgetRef ref, AudioPlayerState audioState, bool isPlaying) {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: IconButton(
        onPressed: () => _onPlayPause(ref, audioState),
        icon: AnimatedSwitcher(
          duration: const Duration(milliseconds: 200),
          child: Icon(
            isPlaying ? AppIcons.pause : AppIcons.play,
            key: ValueKey(isPlaying),
            color: AppColors.primaryBlue,
            size: 28,
          ),
        ),
      ),
    );
  }

  void _onTap(WidgetRef ref, AudioPlayerState audioState) {
    HapticFeedback.mediumImpact();
    if (audioState.currentSong?.id == song.id) {
      _onPlayPause(ref, audioState);
    } else {
      ref.read(audioPlayerProvider.notifier).playSong(song);
    }
  }

  void _onPlayPause(WidgetRef ref, AudioPlayerState audioState) {
    HapticFeedback.mediumImpact();
    final notifier = ref.read(audioPlayerProvider.notifier);
    
    if (audioState.currentSong?.id == song.id) {
      if (audioState.isPlaying) {
        notifier.pause();
      } else {
        notifier.play();
      }
    } else {
      notifier.playSong(song);
    }
  }
}