import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_icons.dart';
import '../../models/song_model.dart';
import '../../providers/audio_player_provider.dart';
import '../../utils/image_utils.dart';

class ModernSongCard extends ConsumerWidget {
  final Song song;
  final bool showIndex;
  final int? index;

  const ModernSongCard({
    super.key,
    required this.song,
    this.showIndex = false,
    this.index,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final audioState = ref.watch(audioPlayerProvider);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final isCurrentSong = audioState.currentSong?.id == song.id;
    final isPlaying = isCurrentSong && audioState.isPlaying;

    return Container(
      height: 64,
      decoration: BoxDecoration(
        color: isCurrentSong
            ? (isDark ? AppColors.primaryBlueLight.withOpacity(0.1) : AppColors.primaryBlue.withOpacity(0.1))
            : (isDark ? AppColors.cardDark : AppColors.cardLight),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCurrentSong
              ? (isDark ? AppColors.primaryBlueLight.withOpacity(0.3) : AppColors.primaryBlue.withOpacity(0.3))
              : (isDark ? AppColors.cardDarkBorder : AppColors.cardLightBorder),
          width: 1,
        ),
        boxShadow: isCurrentSong
            ? [
                BoxShadow(
                  color: (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue).withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : [
                BoxShadow(
                  color: isDark ? Colors.black26 : Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _onTap(ref, audioState),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Index or album art
                if (showIndex && index != null)
                  _buildIndexNumber(isDark, isCurrentSong)
                else
                  _buildAlbumArt(isDark),
                
                const SizedBox(width: 12),
                
                // Song info
                Expanded(
                  child: _buildSongInfo(isDark, isCurrentSong),
                ),
                
                // Play button and more options
                _buildTrailingActions(ref, audioState, isDark, isPlaying),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIndexNumber(bool isDark, bool isCurrentSong) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: isCurrentSong
            ? (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Text(
          '${index! + 1}',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: isCurrentSong
                ? Colors.white
                : (isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight),
          ),
        ),
      ),
    );
  }

  Widget _buildAlbumArt(bool isDark) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: isDark ? Colors.black38 : Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: song.imageUrl != null
            ? CachedNetworkImage(
                imageUrl: song.imageUrl!,
                fit: BoxFit.cover,
                placeholder: (context, url) => _buildPlaceholder(isDark),
                errorWidget: (context, url, error) => _buildPlaceholder(isDark),
              )
            : _buildPlaceholder(isDark),
      ),
    );
  }

  Widget _buildPlaceholder(bool isDark) {
    return Container(
      color: isDark ? AppColors.neutral800 : AppColors.neutral200,
      child: Icon(
        AppIcons.music,
        color: isDark ? AppColors.neutral600 : AppColors.neutral400,
        size: 16,
      ),
    );
  }

  Widget _buildSongInfo(bool isDark, bool isCurrentSong) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          song.title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: isCurrentSong
                ? (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue)
                : (isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 2),
        Row(
          children: [
            if (song.songNumber != null) ...[
              Text(
                '#${song.songNumber}',
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: isDark ? AppColors.textTertiaryDark : AppColors.textTertiaryLight,
                ),
              ),
              const SizedBox(width: 4),
              Container(
                width: 2,
                height: 2,
                decoration: BoxDecoration(
                  color: isDark ? AppColors.textTertiaryDark : AppColors.textTertiaryLight,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 4),
            ],
            Expanded(
              child: Text(
                song.artist,
                style: TextStyle(
                  fontSize: 12,
                  color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTrailingActions(WidgetRef ref, AudioPlayerState audioState, bool isDark, bool isPlaying) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Favorite button
        _buildActionButton(
          icon: AppIcons.favorite,
          onPressed: () {
            HapticFeedback.lightImpact();
            // TODO: Toggle favorite
          },
          isDark: isDark,
          size: 20,
        ),
        
        const SizedBox(width: 8),
        
        // Play/Pause button
        _buildActionButton(
          icon: isPlaying ? AppIcons.pause : AppIcons.play,
          onPressed: () => _onPlayPause(ref, audioState),
          isDark: isDark,
          size: 20,
          isPrimary: true,
        ),
        
        const SizedBox(width: 8),
        
        // More options button
        _buildActionButton(
          icon: Icons.more_vert,
          onPressed: () {
            HapticFeedback.lightImpact();
            _showOptionsBottomSheet(ref.context);
          },
          isDark: isDark,
          size: 20,
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    required bool isDark,
    required double size,
    bool isPrimary = false,
  }) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isPrimary
            ? (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue)
            : Colors.transparent,
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          size: size,
          color: isPrimary
              ? Colors.white
              : (isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight),
        ),
        padding: EdgeInsets.zero,
        splashRadius: 16,
      ),
    );
  }

  void _onTap(WidgetRef ref, AudioPlayerState audioState) {
    HapticFeedback.lightImpact();
    if (audioState.currentSong?.id == song.id) {
      // If it's the current song, toggle play/pause
      _onPlayPause(ref, audioState);
    } else {
      // If it's a different song, play it
      ref.read(audioPlayerProvider.notifier).playSong(song);
    }
  }

  void _onPlayPause(WidgetRef ref, AudioPlayerState audioState) {
    HapticFeedback.lightImpact();
    final notifier = ref.read(audioPlayerProvider.notifier);
    
    if (audioState.currentSong?.id == song.id) {
      if (audioState.isPlaying) {
        notifier.pause();
      } else {
        notifier.play();
      }
    } else {
      notifier.playSong(song);
    }
  }

  void _showOptionsBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => SongOptionsBottomSheet(song: song),
    );
  }
}

class SongOptionsBottomSheet extends StatelessWidget {
  final Song song;

  const SongOptionsBottomSheet({super.key, required this.song});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: isDark ? AppColors.neutral700 : AppColors.neutral300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Song info header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: song.imageUrl != null
                        ? CachedNetworkImage(
                            imageUrl: song.imageUrl!,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: isDark ? AppColors.neutral800 : AppColors.neutral200,
                              child: Icon(
                                AppIcons.music,
                                color: isDark ? AppColors.neutral600 : AppColors.neutral400,
                                size: 20,
                              ),
                            ),
                          )
                        : Container(
                            color: isDark ? AppColors.neutral800 : AppColors.neutral200,
                            child: Icon(
                              AppIcons.music,
                              color: isDark ? AppColors.neutral600 : AppColors.neutral400,
                              size: 20,
                            ),
                          ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        song.title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        song.artist,
                        style: TextStyle(
                          fontSize: 14,
                          color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Options
          _buildOption(
            context,
            icon: AppIcons.favorite,
            title: 'Add to Favorites',
            onTap: () {
              Navigator.pop(context);
              // TODO: Add to favorites
            },
            isDark: isDark,
          ),
          _buildOption(
            context,
            icon: AppIcons.playlist,
            title: 'Add to Playlist',
            onTap: () {
              Navigator.pop(context);
              // TODO: Add to playlist
            },
            isDark: isDark,
          ),
          _buildOption(
            context,
            icon: AppIcons.share,
            title: 'Share',
            onTap: () {
              Navigator.pop(context);
              // TODO: Share song
            },
            isDark: isDark,
          ),
          _buildOption(
            context,
            icon: AppIcons.download,
            title: 'Download',
            onTap: () {
              Navigator.pop(context);
              // TODO: Download song
            },
            isDark: isDark,
          ),
          
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
    );
  }
}