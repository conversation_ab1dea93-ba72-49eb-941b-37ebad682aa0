import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_icons.dart';
import '../../models/week_program_model.dart';
import '../../models/song_model.dart';
import '../../providers/program_provider.dart';
import '../../services/program_service.dart';

class MonthlyProgramCard extends ConsumerStatefulWidget {
  const MonthlyProgramCard({super.key});

  @override
  ConsumerState<MonthlyProgramCard> createState() => _MonthlyProgramCardState();
}

class _MonthlyProgramCardState extends ConsumerState<MonthlyProgramCard> {
  late ProgramService _programService;

  @override
  void initState() {
    super.initState();
    _programService = ProgramService.instance;
  }

  @override
  Widget build(BuildContext context) {
    final monthAsync = ref.watch(currentMonthProvider);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return monthAsync.when(
      data: (month) => month != null
          ? _buildMonthlyProgramContent(month, isDark)
          : _buildEmptyState(isDark),
      loading: () => _buildLoadingState(isDark),
      error: (error, _) => _buildErrorState(isDark),
    );
  }

  Widget _buildMonthlyProgramContent(MonthProgramModel month, bool isDark) {
    if (month.weeks.isEmpty) {
      return _buildEmptyState(isDark);
    }

    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: isDark ? AppColors.cardDark : AppColors.cardLight,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark ? AppColors.cardDarkBorder : AppColors.cardLightBorder,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isDark ? AppColors.primaryBlue.withOpacity(0.2) : AppColors.primaryBlue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    AppIcons.calendar,
                    size: 16,
                    color: isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _getMonthYearText(month),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
                  ),
                ),
                const Spacer(),
                Text(
                  '${month.weeks.length} weeks',
                  style: TextStyle(
                    fontSize: 12,
                    color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
                  ),
                ),
              ],
            ),
          ),

          // Weeks List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: month.weeks.length,
              itemBuilder: (context, index) {
                final week = month.weeks[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: _buildWeekCard(week, index, isDark),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeekCard(WeekProgramModel week, int weekIndex, bool isDark) {
    final isCurrentWeek = _isCurrentWeek(week);
    final weekStart = week.startDate;
    final weekEnd = weekStart.add(const Duration(days: 6));

    return Container(
      decoration: BoxDecoration(
        color: isCurrentWeek
            ? (isDark ? AppColors.primaryBlue.withOpacity(0.1) : AppColors.primaryBlue.withOpacity(0.05))
            : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCurrentWeek
              ? (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue)
              : (isDark ? AppColors.cardDarkBorder : AppColors.cardLightBorder),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _handleWeekPress(week),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Week indicator
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isCurrentWeek
                        ? (isDark ? AppColors.primaryBlue : AppColors.primaryBlue)
                        : (isDark ? AppColors.neutral600 : AppColors.neutral200),
                  ),
                  child: Center(
                    child: Text(
                      '${week.weekNumber}',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: isCurrentWeek
                            ? Colors.white
                            : (isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // Week details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _formatWeekDate(weekStart, weekEnd),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: isCurrentWeek
                              ? (isDark ? AppColors.primaryBlueLight : AppColors.primaryBlue)
                              : (isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight),
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        _getWeekSongsPreview(week.songs),
                        style: TextStyle(
                          fontSize: 12,
                          color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),

                // Arrow indicator
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState(bool isDark) {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: isDark ? AppColors.cardDark : AppColors.cardLight,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark ? AppColors.cardDarkBorder : AppColors.cardLightBorder,
          width: 1,
        ),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildEmptyState(bool isDark) {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: isDark ? AppColors.cardDark : AppColors.cardLight,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDark ? AppColors.cardDarkBorder : AppColors.cardLightBorder,
          width: 1,
        ),
      ),
      child: const Center(
        child: Text('No monthly program available'),
      ),
    );
  }

  Widget _buildErrorState(bool isDark) {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: isDark ? AppColors.cardDark : AppColors.cardLight,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.error,
          width: 1,
        ),
      ),
      child: const Center(
        child: Text('Error loading monthly program'),
      ),
    );
  }

  String _getMonthYearText(MonthProgramModel month) {
    final monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${monthNames[month.month - 1]} ${month.year}';
  }

  String _formatWeekDate(DateTime start, DateTime end) {
    final startDay = start.day;
    final endDay = end.day;
    final startMonth = _getMonthName(start.month);
    final endMonth = _getMonthName(end.month);

    if (start.month == end.month) {
      return '$startDay-$endDay $startMonth';
    } else {
      return '$startDay $startMonth - $endDay $endMonth';
    }
  }

  String _getMonthName(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }

  String _getWeekSongsPreview(List<int> songs) {
    if (songs.isEmpty) return 'No hymns';

    final songNumbers = songs.take(4).join(', ');
    if (songs.length > 4) {
      return '$songNumbers...';
    }
    return songNumbers;
  }

  bool _isCurrentWeek(WeekProgramModel week) {
    final today = DateTime.now();
    final weekStart = week.startDate;
    final weekEnd = weekStart.add(const Duration(days: 6));

    return today.isAfter(weekStart.subtract(const Duration(days: 1))) &&
           today.isBefore(weekEnd.add(const Duration(days: 1)));
  }

  void _handleWeekPress(WeekProgramModel week) {
    HapticFeedback.lightImpact();

    // Navigate to week detail or show week songs
    debugPrint('Selected week: ${week.weekNumber} (${_formatWeekDate(week.startDate, week.startDate.add(const Duration(days: 6)))})');
    // TODO: Navigate to week detail screen or expand to show songs
  }
}