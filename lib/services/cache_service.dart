import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import '../models/song_model.dart';
import '../models/month_program_model.dart';

/// Cache service with 24-hour expiration for NiseAmen data
class CacheService {
  static CacheService? _instance;
  static CacheService get instance => _instance ??= CacheService._();

  CacheService._();

  late Box<dynamic> _cacheBox;
  late SharedPreferences _prefs;
  bool _initialized = false;

  /// Initialize the cache service
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      await Hive.initFlutter();
      
      // Register Hive adapters if not already registered
      // Note: These adapters need to be generated with build_runner
      // For now, we'll skip adapter registration and use JSON serialization

      _cacheBox = await Hive.openBox('niseamen_cache');
      _prefs = await SharedPreferences.getInstance();
      _initialized = true;

      debugPrint('CacheService initialized');
    } catch (e) {
      debugPrint('Error initializing CacheService: $e');
      rethrow;
    }
  }

  /// Check if cache is initialized
  void _ensureInitialized() {
    if (!_initialized) {
      throw StateError('CacheService not initialized. Call initialize() first.');
    }
  }

  /// Cache songs data using JSON serialization
  Future<void> cacheSongs(List<Song> songs) async {
    _ensureInitialized();
    
    try {
      // Convert songs to JSON-serializable format
      final serializedSongs = songs.map((song) => song.toJson()).toList();
      await _cacheBox.put(AppConstants.songsCache, jsonEncode(serializedSongs));
      await _setCacheTimestamp(AppConstants.songsCache);
      
      debugPrint('Cached ${songs.length} songs');
    } catch (e) {
      debugPrint('Error caching songs: $e');
    }
  }

  /// Get cached songs using JSON deserialization
  Future<List<Song>?> getCachedSongs() async {
    _ensureInitialized();
    
    try {
      if (!_isCacheValid(AppConstants.songsCache)) {
        return null;
      }
      
      final jsonString = _cacheBox.get(AppConstants.songsCache) as String?;
      if (jsonString == null) return null;
      
      final serializedSongs = jsonDecode(jsonString) as List<dynamic>;
      return serializedSongs.map((json) => Song.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting cached songs: $e');
      return null;
    }
  }

  /// Cache monthly program data using JSON serialization
  Future<void> cacheMonthProgram(MonthProgram program) async {
    _ensureInitialized();
    
    try {
      final key = '${AppConstants.programCache}_${program.documentId}';
      await _cacheBox.put(key, jsonEncode(program.toJson()));
      await _setCacheTimestamp(key);
      
      debugPrint('Cached month program: ${program.documentId}');
    } catch (e) {
      debugPrint('Error caching month program: $e');
    }
  }

  /// Get cached monthly program using JSON deserialization
  Future<MonthProgram?> getCachedMonthProgram(String documentId) async {
    _ensureInitialized();
    
    try {
      final key = '${AppConstants.programCache}_$documentId';
      
      if (!_isCacheValid(key)) {
        return null;
      }
      
      final jsonString = _cacheBox.get(key) as String?;
      if (jsonString == null) return null;
      
      return MonthProgram.fromJson(jsonDecode(jsonString));
    } catch (e) {
      debugPrint('Error getting cached month program: $e');
      return null;
    }
  }

  /// Cache featured song data
  Future<void> cacheFeaturedSong(String songId, DateTime forDate) async {
    _ensureInitialized();
    
    try {
      final dateKey = _getDateKey(forDate);
      final key = '${AppConstants.featuredSongCache}_$dateKey';
      
      await _cacheBox.put(key, songId);
      await _setCacheTimestamp(key);
      
      debugPrint('Cached featured song $songId for $dateKey');
    } catch (e) {
      debugPrint('Error caching featured song: $e');
    }
  }

  /// Get cached featured song for a specific date
  Future<String?> getCachedFeaturedSong(DateTime forDate) async {
    _ensureInitialized();
    
    try {
      final dateKey = _getDateKey(forDate);
      final key = '${AppConstants.featuredSongCache}_$dateKey';
      
      if (!_isCacheValid(key)) {
        return null;
      }
      
      return _cacheBox.get(key) as String?;
    } catch (e) {
      debugPrint('Error getting cached featured song: $e');
      return null;
    }
  }

  /// Cache lyrics data
  Future<void> cacheLyrics(String songId, String language, String lyrics) async {
    _ensureInitialized();
    
    try {
      final key = '${AppConstants.lyricsCache}_${songId}_$language';
      await _cacheBox.put(key, lyrics);
      await _setCacheTimestamp(key);
      
      debugPrint('Cached lyrics for song $songId in $language');
    } catch (e) {
      debugPrint('Error caching lyrics: $e');
    }
  }

  /// Get cached lyrics
  Future<String?> getCachedLyrics(String songId, String language) async {
    _ensureInitialized();
    
    try {
      final key = '${AppConstants.lyricsCache}_${songId}_$language';
      
      if (!_isCacheValid(key)) {
        return null;
      }
      
      return _cacheBox.get(key) as String?;
    } catch (e) {
      debugPrint('Error getting cached lyrics: $e');
      return null;
    }
  }

  /// Cache generic JSON data
  Future<void> cacheJson(String key, Map<String, dynamic> data) async {
    _ensureInitialized();
    
    try {
      await _cacheBox.put(key, jsonEncode(data));
      await _setCacheTimestamp(key);
      
      debugPrint('Cached JSON data for key: $key');
    } catch (e) {
      debugPrint('Error caching JSON data: $e');
    }
  }

  /// Get cached JSON data
  Future<Map<String, dynamic>?> getCachedJson(String key) async {
    _ensureInitialized();
    
    try {
      if (!_isCacheValid(key)) {
        return null;
      }
      
      final jsonString = _cacheBox.get(key) as String?;
      if (jsonString == null) return null;
      
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error getting cached JSON data: $e');
      return null;
    }
  }

  /// Check if cache is valid (not expired)
  bool _isCacheValid(String key) {
    try {
      final timestampKey = '${key}_timestamp';
      final timestamp = _prefs.getInt(timestampKey);
      
      if (timestamp == null) return false;
      
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
      final now = DateTime.now();
      final difference = now.difference(cacheTime);
      
      return difference < AppConstants.cacheDuration;
    } catch (e) {
      debugPrint('Error checking cache validity: $e');
      return false;
    }
  }

  /// Set cache timestamp
  Future<void> _setCacheTimestamp(String key) async {
    try {
      final timestampKey = '${key}_timestamp';
      await _prefs.setInt(timestampKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      debugPrint('Error setting cache timestamp: $e');
    }
  }

  /// Get date key for cache (YYYY-MM-DD format)
  String _getDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Clear expired cache entries
  Future<void> clearExpiredCache() async {
    _ensureInitialized();
    
    try {
      final keys = _cacheBox.keys.toList();
      int removedCount = 0;
      
      for (final key in keys) {
        if (!_isCacheValid(key.toString())) {
          await _cacheBox.delete(key);
          
          // Also remove timestamp
          final timestampKey = '${key}_timestamp';
          await _prefs.remove(timestampKey);
          
          removedCount++;
        }
      }
      
      debugPrint('Cleared $removedCount expired cache entries');
    } catch (e) {
      debugPrint('Error clearing expired cache: $e');
    }
  }

  /// Clear all cache
  Future<void> clearAllCache() async {
    _ensureInitialized();
    
    try {
      await _cacheBox.clear();
      
      // Clear all cache timestamps
      final keys = _prefs.getKeys().where((key) => key.endsWith('_timestamp')).toList();
      for (final key in keys) {
        await _prefs.remove(key);
      }
      
      debugPrint('Cleared all cache');
    } catch (e) {
      debugPrint('Error clearing all cache: $e');
    }
  }

  /// Get cache size in bytes (approximate)
  Future<int> getCacheSize() async {
    _ensureInitialized();
    
    try {
      int totalSize = 0;
      
      for (final key in _cacheBox.keys) {
        final value = _cacheBox.get(key);
        if (value is String) {
          totalSize += value.length * 2; // Approximate UTF-16 encoding
        } else if (value is List) {
          totalSize += value.length * 100; // Rough estimate for complex objects
        } else {
          totalSize += 50; // Default estimate
        }
      }
      
      return totalSize;
    } catch (e) {
      debugPrint('Error calculating cache size: $e');
      return 0;
    }
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    _ensureInitialized();
    
    try {
      final totalEntries = _cacheBox.length;
      int validEntries = 0;
      int expiredEntries = 0;
      
      for (final key in _cacheBox.keys) {
        if (_isCacheValid(key.toString())) {
          validEntries++;
        } else {
          expiredEntries++;
        }
      }
      
      final cacheSize = await getCacheSize();
      
      return {
        'totalEntries': totalEntries,
        'validEntries': validEntries,
        'expiredEntries': expiredEntries,
        'cacheSize': cacheSize,
        'cacheSizeFormatted': _formatBytes(cacheSize),
      };
    } catch (e) {
      debugPrint('Error getting cache stats: $e');
      return {};
    }
  }

  /// Format bytes to human readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Force refresh cache for a specific key
  Future<void> invalidateCache(String key) async {
    _ensureInitialized();
    
    try {
      await _cacheBox.delete(key);
      
      final timestampKey = '${key}_timestamp';
      await _prefs.remove(timestampKey);
      
      debugPrint('Invalidated cache for key: $key');
    } catch (e) {
      debugPrint('Error invalidating cache: $e');
    }
  }

  /// Check if data exists in cache (regardless of expiration)
  bool hasCache(String key) {
    _ensureInitialized();
    return _cacheBox.containsKey(key);
  }

  /// Get cached data even if expired (fallback mode)
  Future<T?> getCachedDataFallback<T>(String key) async {
    _ensureInitialized();
    
    try {
      return _cacheBox.get(key) as T?;
    } catch (e) {
      debugPrint('Error getting fallback cache data: $e');
      return null;
    }
  }

  /// Close cache service
  Future<void> close() async {
    if (_initialized) {
      await _cacheBox.close();
      _initialized = false;
    }
  }
}
