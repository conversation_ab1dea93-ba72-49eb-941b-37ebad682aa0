import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/song_model.dart';
import 'firestore_service.dart';

/// Service for managing user-related operations
class UserService {
  static UserService? _instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  UserService._();

  /// Singleton instance getter
  static UserService get instance {
    _instance ??= UserService._();
    return _instance!;
  }

  /// Get current user ID
  String? get currentUserId => _auth.currentUser?.uid;

  /// Add a song to the user's recently played list
  Future<void> addToRecentlyPlayed(Song song) async {
    final userId = currentUserId;
    if (userId == null) {
      // User not authenticated, can't track recently played
      return;
    }

    try {
      final userDocRef = _firestore.collection('users').doc(userId);
      
      // Use a transaction to ensure atomic update
      await _firestore.runTransaction((transaction) async {
        final userDoc = await transaction.get(userDocRef);
        
        if (!userDoc.exists) {
          // Create user document if it doesn't exist
          transaction.set(userDocRef, {
            'recentlyPlayed': [song.id],
            'createdAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });
          return;
        }

        // Get current recently played list
        final currentRecentlyPlayed = (userDoc.data()?['recentlyPlayed'] as List<dynamic>?)?.cast<String>() ?? [];
        
        // Remove the song if it already exists to avoid duplicates
        final updatedRecentlyPlayed = currentRecentlyPlayed.where((id) => id != song.id).toList();
        
        // Add the new song to the beginning of the list
        updatedRecentlyPlayed.insert(0, song.id);
        
        // Limit to last 20 songs
        final finalRecentlyPlayed = updatedRecentlyPlayed.take(20).toList();
        
        // Update the document
        transaction.update(userDocRef, {
          'recentlyPlayed': finalRecentlyPlayed,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      });
      
    } catch (e) {
      print('Error updating recently played: $e');
      // Don't throw, this is a non-critical operation
    }
  }

  /// Get user's recently played songs
  Future<List<String>> getRecentlyPlayedSongIds() async {
    final userId = currentUserId;
    if (userId == null) {
      return [];
    }

    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      
      if (!userDoc.exists) {
        return [];
      }

      final recentlyPlayed = (userDoc.data()?['recentlyPlayed'] as List<dynamic>?)?.cast<String>() ?? [];
      return recentlyPlayed;
    } catch (e) {
      print('Error getting recently played: $e');
      return [];
    }
  }

  /// Clear user's recently played list
  Future<void> clearRecentlyPlayed() async {
    final userId = currentUserId;
    if (userId == null) {
      return;
    }

    try {
      await _firestore.collection('users').doc(userId).update({
        'recentlyPlayed': [],
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Error clearing recently played: $e');
    }
  }
}
