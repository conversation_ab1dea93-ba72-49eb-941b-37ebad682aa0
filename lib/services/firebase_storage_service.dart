import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';

/// Firebase Storage service for NiseAmen audio files
class FirebaseStorageService {
  static FirebaseStorageService? _instance;
  static FirebaseStorageService get instance => _instance ??= FirebaseStorageService._();

  FirebaseStorageService._();

  final FirebaseStorage _storage = FirebaseStorage.instance;

  /// Get download URL for an audio file
  /// [storagePath] should be in format: "music/001 Cantique ECC.mp3"
  Future<String> getAudioUrl(String storagePath) async {
    try {
      final ref = _storage.ref(storagePath);
      return await ref.getDownloadURL();
    } catch (e) {
      debugPrint('Error getting audio URL for $storagePath: $e');
      rethrow;
    }
  }

  /// Get download URL for an audio file by song ID
  /// [songId] should be the numeric ID like "1", "2", etc.
  Future<String> getAudioUrlBySongId(String songId) async {
    try {
      // Convert songId to padded format (e.g., "1" -> "001")
      final paddedId = songId.padLeft(3, '0');
      final storagePath = '${AppConstants.firebaseStorageMusicPath}$paddedId Cantique ECC.mp3';
      
      return await getAudioUrl(storagePath);
    } catch (e) {
      debugPrint('Error getting audio URL for song ID $songId: $e');
      rethrow;
    }
  }

  /// Get download URLs for multiple audio files
  Future<Map<String, String>> getMultipleAudioUrls(List<String> storagePaths) async {
    final urls = <String, String>{};
    
    final futures = storagePaths.map((path) async {
      try {
        final url = await getAudioUrl(path);
        urls[path] = url;
      } catch (e) {
        debugPrint('Error getting URL for $path: $e');
        // Don't add to map if failed
      }
    });

    await Future.wait(futures);
    return urls;
  }

  /// Get download URLs for multiple songs by IDs
  Future<Map<String, String>> getMultipleAudioUrlsBySongIds(List<String> songIds) async {
    final urls = <String, String>{};
    
    final futures = songIds.map((songId) async {
      try {
        final url = await getAudioUrlBySongId(songId);
        urls[songId] = url;
      } catch (e) {
        debugPrint('Error getting URL for song ID $songId: $e');
        // Don't add to map if failed
      }
    });

    await Future.wait(futures);
    return urls;
  }

  /// Check if a file exists in Firebase Storage
  Future<bool> fileExists(String storagePath) async {
    try {
      final ref = _storage.ref(storagePath);
      await ref.getMetadata();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get file metadata
  Future<FullMetadata?> getFileMetadata(String storagePath) async {
    try {
      final ref = _storage.ref(storagePath);
      return await ref.getMetadata();
    } catch (e) {
      debugPrint('Error getting metadata for $storagePath: $e');
      return null;
    }
  }

  /// Get file size in bytes
  Future<int?> getFileSize(String storagePath) async {
    try {
      final metadata = await getFileMetadata(storagePath);
      return metadata?.size;
    } catch (e) {
      debugPrint('Error getting file size for $storagePath: $e');
      return null;
    }
  }

  /// List all files in the music directory
  Future<List<String>> listMusicFiles() async {
    try {
      final ref = _storage.ref(AppConstants.firebaseStorageMusicPath);
      final result = await ref.listAll();
      
      return result.items.map((item) => item.fullPath).toList();
    } catch (e) {
      debugPrint('Error listing music files: $e');
      return [];
    }
  }

  /// Get audio URL with retry logic
  Future<String> getAudioUrlWithRetry(
    String storagePath, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await getAudioUrl(storagePath);
      } catch (e) {
        attempts++;
        if (attempts >= maxRetries) {
          rethrow;
        }
        
        debugPrint('Attempt $attempts failed for $storagePath, retrying in ${retryDelay.inSeconds}s...');
        await Future.delayed(retryDelay);
      }
    }
    
    throw Exception('Failed to get audio URL after $maxRetries attempts');
  }

  /// Batch get audio URLs with timeout and error handling
  Future<Map<String, String>> batchGetAudioUrls(
    List<String> storagePaths, {
    Duration timeout = const Duration(seconds: 10),
  }) async {
    final urls = <String, String>{};
    
    try {
      final futures = storagePaths.map((path) async {
        try {
          final url = await getAudioUrl(path).timeout(timeout);
          return MapEntry(path, url);
        } catch (e) {
          debugPrint('Error getting URL for $path: $e');
          return null;
        }
      });

      final results = await Future.wait(futures);
      
      for (final result in results) {
        if (result != null) {
          urls[result.key] = result.value;
        }
      }
    } catch (e) {
      debugPrint('Error in batch get audio URLs: $e');
    }
    
    return urls;
  }

  /// Convert Firebase Storage path to download URL with caching
  static final Map<String, String> _urlCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(hours: 1);

  Future<String> getCachedAudioUrl(String storagePath) async {
    final now = DateTime.now();
    
    // Check if we have a cached URL that's still valid
    if (_urlCache.containsKey(storagePath) && 
        _cacheTimestamps.containsKey(storagePath)) {
      final cacheTime = _cacheTimestamps[storagePath]!;
      if (now.difference(cacheTime) < _cacheExpiry) {
        return _urlCache[storagePath]!;
      }
    }
    
    // Get fresh URL and cache it
    try {
      final url = await getAudioUrl(storagePath);
      _urlCache[storagePath] = url;
      _cacheTimestamps[storagePath] = now;
      return url;
    } catch (e) {
      // If we have a cached URL, return it even if expired
      if (_urlCache.containsKey(storagePath)) {
        debugPrint('Using expired cached URL for $storagePath due to error: $e');
        return _urlCache[storagePath]!;
      }
      rethrow;
    }
  }

  /// Clear URL cache
  void clearCache() {
    _urlCache.clear();
    _cacheTimestamps.clear();
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    final now = DateTime.now();
    int validEntries = 0;
    int expiredEntries = 0;
    
    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) < _cacheExpiry) {
        validEntries++;
      } else {
        expiredEntries++;
      }
    }
    
    return {
      'totalEntries': _urlCache.length,
      'validEntries': validEntries,
      'expiredEntries': expiredEntries,
    };
  }
}