import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/week_program_model.dart';
import '../models/song_model.dart';
import 'cache_service.dart';

class ProgramService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final CacheService _cacheService = CacheService();

  static const String _programCacheKey = 'weekly_program';
  static const String _featuredSongCacheKey = 'featured_song';
  static const Duration _cacheExpiration = Duration(hours: 24);

  // Get current date info in GMT+1 timezone
  Map<String, dynamic> _getCurrentDateInfo() {
    final now = DateTime.now();

    // Convert to GMT+1 by adding one hour
    final gmtPlus1Time = DateTime.utc(
      now.year,
      now.month,
      now.day,
      now.hour + 1,
      now.minute,
      now.second,
    );

    // Extract date components
    final year = gmtPlus1Time.year;
    final month = gmtPlus1Time.month;
    final date = gmtPlus1Time.day;

    // Get day of week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
    final dayOfWeek = gmtPlus1Time.weekday;

    // Convert to Monday-based index (0 = Monday, 6 = Sunday)
    final songIndex = dayOfWeek == DateTime.sunday ? 6 : dayOfWeek - 1;

    // Calculate ISO week number
    final weekNumber = _getISOWeek(gmtPlus1Time);

    return {
      'year': year,
      'month': month,
      'date': date,
      'dayOfWeek': songIndex, // Index for songs array (0 = Monday, 6 = Sunday)
      'weekNumber': weekNumber,
      'dateString': '$year-$month-$date',
    };
  }

  int _getISOWeek(DateTime date) {
    final d = DateTime.utc(date.year, date.month, date.day);
    final dayNum = d.weekday == 7 ? 7 : d.weekday;
    d.add(Duration(days: 4 - dayNum));

    final yearStart = DateTime.utc(d.year, 1, 1);
    return ((d.difference(yearStart).inDays + 1) / 7).ceil();
  }

  // Fetch current month's program
  Future<MonthProgramModel?> fetchCurrentMonthProgram({bool forceRefresh = false}) async {
    try {
      final dateInfo = _getCurrentDateInfo();
      final year = dateInfo['year'];
      final month = dateInfo['month'];

      // Check cache first
      if (!forceRefresh) {
        final cached = await _cacheService.get(_programCacheKey);
        if (cached != null && cached is Map) {
          final timestamp = cached['timestamp'] as int?;
          if (timestamp != null &&
              DateTime.now().millisecondsSinceEpoch - timestamp < _cacheExpiration.inMilliseconds) {
            final programData = cached['data'];
            if (programData != null) {
              return MonthProgramModel.fromJson(programData);
            }
          }
        }
      }

      // Convert month number to month name
      final months = [
        'january', 'february', 'march', 'april', 'may', 'june',
        'july', 'august', 'september', 'october', 'november', 'december'
      ];
      final monthName = months[month - 1];

      // Fetch from Firestore
      final docId = '${monthName}_$year';
      final docRef = _firestore.collection('cantique_program').doc(docId);
      final docSnap = await docRef.get();

      if (docSnap.exists) {
        final program = MonthProgramModel.fromFirestore(docSnap);

        // Cache the result
        await _cacheService.set(_programCacheKey, {
          'data': program.toJson(),
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        });

        return program;
      }

      // Generate fallback if no data found
      return _generateFallbackProgram(year, month);
    } catch (e) {
      debugPrint('Error fetching month program: $e');
      final dateInfo = _getCurrentDateInfo();
      return _generateFallbackProgram(dateInfo['year'], dateInfo['month']);
    }
  }

  // Get current week's program
  Future<WeekProgramModel?> getCurrentWeekProgram({bool forceRefresh = false}) async {
    try {
      final monthProgram = await fetchCurrentMonthProgram(forceRefresh: forceRefresh);

      if (monthProgram == null || monthProgram.weeks.isEmpty) {
        return _generateFallbackWeek();
      }

      final dateInfo = _getCurrentDateInfo();
      final today = DateTime.now();

      // Find the week that contains today's date
      for (final week in monthProgram.weeks) {
        final weekStart = week.startDate;
        final weekEnd = weekStart.add(const Duration(days: 6));

        if (today.isAfter(weekStart.subtract(const Duration(days: 1))) &&
            today.isBefore(weekEnd.add(const Duration(days: 1)))) {
          return week;
        }
      }

      // Return first week as fallback
      return monthProgram.weeks.first;
    } catch (e) {
      debugPrint('Error getting current week: $e');
      return _generateFallbackWeek();
    }
  }

  // Get today's featured song
  Future<int?> getTodaysFeaturedSong({bool forceRefresh = false}) async {
    try {
      final dateInfo = _getCurrentDateInfo();
      final dayOfWeek = dateInfo['dayOfWeek'];
      final dateString = dateInfo['dateString'];

      // Check cache first
      if (!forceRefresh) {
        final cached = await _cacheService.get(_featuredSongCacheKey);
        if (cached != null && cached is Map) {
          final songNumber = cached['songNumber'];
          final date = cached['date'];
          if (date == dateString && songNumber != null) {
            return songNumber as int;
          }
        }
      }

      final currentWeek = await getCurrentWeekProgram(forceRefresh: forceRefresh);

      if (currentWeek != null &&
          currentWeek.songs.isNotEmpty &&
          dayOfWeek < currentWeek.songs.length) {

        final featuredSong = currentWeek.songs[dayOfWeek];

        // Cache the result
        await _cacheService.set(_featuredSongCacheKey, {
          'songNumber': featuredSong,
          'date': dateString,
        });

        return featuredSong;
      }

      // Return fallback song
      return _getFallbackFeaturedSong(dayOfWeek);
    } catch (e) {
      debugPrint('Error getting featured song: $e');
      final dateInfo = _getCurrentDateInfo();
      return _getFallbackFeaturedSong(dateInfo['dayOfWeek']);
    }
  }

  // Generate fallback month program
  MonthProgramModel _generateFallbackProgram(int year, int month) {
    final weeks = <WeekProgramModel>[];

    // Get first day of month
    final firstDayOfMonth = DateTime.utc(year, month, 1);

    // Calculate Monday of the week containing first day
    final dayOfWeek = firstDayOfMonth.weekday;
    final daysToAdjust = dayOfWeek == DateTime.sunday ? 6 : dayOfWeek - 1;
    final firstMonday = DateTime.utc(year, month, firstDayOfMonth.day - daysToAdjust);

    // Generate 4-5 weeks
    for (int i = 0; i < 5; i++) {
      final weekStart = DateTime.utc(
        firstMonday.year,
        firstMonday.month,
        firstMonday.day + (i * 7),
      );

      if (weekStart.month > month) break;

      final songs = List.generate(7, (index) {
        final hash = (weekStart.millisecondsSinceEpoch + index * 86400000) % 300;
        return (hash % 300 + 1).toInt();
      });

      weeks.add(WeekProgramModel(
        weekNumber: _getISOWeek(weekStart),
        startDate: weekStart,
        songs: songs,
      ));
    }

    return MonthProgramModel(
      year: year,
      month: month,
      weeks: weeks,
    );
  }

  // Generate fallback week
  WeekProgramModel _generateFallbackWeek() {
    final dateInfo = _getCurrentDateInfo();
    final weekNumber = dateInfo['weekNumber'];

    // Get Monday of current week
    final today = DateTime.now();
    final dayOfWeek = today.weekday;
    final daysToAdjust = dayOfWeek == DateTime.sunday ? 6 : dayOfWeek - 1;
    final monday = DateTime.utc(
      today.year,
      today.month,
      today.day - daysToAdjust,
    );

    final songs = List.generate(7, (index) {
      final hash = (monday.millisecondsSinceEpoch + index * 86400000) % 300;
      return (hash % 300 + 1).toInt();
    });

    return WeekProgramModel(
      weekNumber: weekNumber,
      startDate: monday,
      songs: songs,
    );
  }

  // Get fallback featured song
  int _getFallbackFeaturedSong(int dayOfWeek) {
    final today = DateTime.now();
    final hash = (today.millisecondsSinceEpoch + dayOfWeek * 86400000) % 300;
    return (hash % 300 + 1).toInt();
  }

  // Format week date for display
  String formatWeekDate(WeekProgramModel week) {
    if (week.startDate.year == 1) return 'Current Week';

    final startDay = week.startDate.day;
    final endDay = week.startDate.add(const Duration(days: 6)).day;
    final startMonth = week.startDate.month;
    final endMonth = week.startDate.add(const Duration(days: 6)).month;
    final startMonthName = _getMonthName(startMonth);
    final endMonthName = _getMonthName(endMonth);

    if (startMonth == endMonth) {
      return 'Week ${week.weekNumber} ($startDay-$endDay $startMonthName)';
    } else {
      return 'Week ${week.weekNumber} ($startDay $startMonthName - $endDay $endMonthName)';
    }
  }

  // Format day of week
  String formatDayOfWeek(int dayIndex) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[dayIndex];
  }

  String _getMonthName(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }
}