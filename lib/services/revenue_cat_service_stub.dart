import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import '../config/api_config.dart';
import '../models/subscription_models.dart';

/// Stub RevenueCat service for handling in-app purchases and subscriptions
/// This is a temporary implementation that returns default values
class RevenueCatService {
  static RevenueCatService? _instance;
  static RevenueCatService get instance => _instance ??= RevenueCatService._();

  RevenueCatService._();

  bool _isInitialized = false;

  /// Initialize RevenueCat SDK (stub implementation)
  Future<void> initialize({String? userId}) async {
    try {
      debugPrint('RevenueCat stub service initialized (no actual functionality)');
      if (userId != null) {
        debugPrint('RevenueCat stub: Setting user ID during init: $userId');
      }
      _isInitialized = true;
    } catch (e) {
      debugPrint('RevenueCat stub initialization error: $e');
      rethrow;
    }
  }

  /// Get subscription status (stub implementation)
  Future<SubscriptionStatus> getSubscriptionStatus() async {
    if (!_isInitialized) {
      throw Exception('RevenueCat not initialized. Call initialize() first.');
    }

    // Return default free status
    return const SubscriptionStatus(
      isActive: false,
      isPremium: false,
      willRenew: false,
      store: 'stub',
    );
  }

  /// Get available subscription plans (stub implementation)
  Future<List<SubscriptionPlan>> getAvailablePlans() async {
    if (!_isInitialized) {
      throw Exception('RevenueCat not initialized. Call initialize() first.');
    }

    // Return empty list
    return [];
  }

  /// Purchase a subscription plan (stub implementation)
  Future<PurchaseResult> purchasePlan(SubscriptionPlan plan) async {
    if (!_isInitialized) {
      throw Exception('RevenueCat not initialized. Call initialize() first.');
    }

    return PurchaseResult.error('Subscriptions not available in stub implementation');
  }

  /// Restore purchases (stub implementation)
  Future<PurchaseResult> restorePurchases() async {
    if (!_isInitialized) {
      throw Exception('RevenueCat not initialized. Call initialize() first.');
    }

    return PurchaseResult.success(
      productId: 'stub',
      subscriptionStatus: const SubscriptionStatus(
        isActive: false,
        isPremium: false,
        willRenew: false,
        store: 'stub',
      ),
    );
  }

  /// Set user ID (stub implementation)
  Future<void> setUserId(String userId) async {
    if (!_isInitialized) {
      throw Exception('RevenueCat not initialized. Call initialize() first.');
    }
    debugPrint('RevenueCat stub: Setting user ID to $userId');
  }

  /// Log out user (stub implementation)
  Future<void> logOut() async {
    if (!_isInitialized) {
      throw Exception('RevenueCat not initialized. Call initialize() first.');
    }
    debugPrint('RevenueCat stub: Logging out user');
  }

  /// Check if RevenueCat is configured
  bool get isConfigured => false; // Always false for stub

  /// Get current user's subscription info
  Future<Map<String, dynamic>> getSubscriptionInfo() async {
    return {
      'isSubscribed': false,
      'planName': null,
      'expirationDate': null,
      'isTrialPeriod': false,
      'willRenew': false,
    };
  }

  /// Check if user has specific entitlement (stub implementation)
  Future<bool> hasEntitlement(String entitlementId) async {
    if (!_isInitialized) {
      throw Exception('RevenueCat not initialized. Call initialize() first.');
    }
    return false; // Always false in stub
  }

  /// Login user (stub implementation)
  Future<void> loginUser(String userId) async {
    if (!_isInitialized) {
      throw Exception('RevenueCat not initialized. Call initialize() first.');
    }
    debugPrint('RevenueCat stub: Logging in user $userId');
  }

  /// Logout user (stub implementation)
  Future<void> logoutUser() async {
    if (!_isInitialized) {
      throw Exception('RevenueCat not initialized. Call initialize() first.');
    }
    debugPrint('RevenueCat stub: Logging out user');
  }
}