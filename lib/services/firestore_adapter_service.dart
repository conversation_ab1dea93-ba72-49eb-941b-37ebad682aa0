import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/song_model.dart';
import '../models/month_program_model.dart';
import '../constants/app_constants.dart';
import 'firebase_storage_service.dart';

/// Adapter service to work with your existing Firestore database structure
class FirestoreAdapterService {
  static FirestoreAdapterService? _instance;
  static FirestoreAdapterService get instance => _instance ??= FirestoreAdapterService._();

  FirestoreAdapterService._();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorageService _storage = FirebaseStorageService.instance;

  /// Get all songs from your existing Firestore structure
  Future<List<Song>> getAllSongs() async {
    try {
      debugPrint('🔍 Fetching songs from Firestore...');
      final querySnapshot = await _firestore.collection('songs').get();
      debugPrint('📊 Found ${querySnapshot.docs.length} documents in songs collection');
      
      final songs = <Song>[];

      for (final doc in querySnapshot.docs) {
        try {
          debugPrint('🎵 Processing song: ${doc.id}');
          debugPrint('📄 Song data: ${doc.data()}');
          
          final song = await _adaptSongDocument(doc);
          if (song != null) {
            songs.add(song);
            debugPrint('✅ Successfully adapted song: ${song.title}');
          } else {
            debugPrint('❌ Failed to adapt song: ${doc.id}');
          }
        } catch (e) {
          debugPrint('💥 Error processing song ${doc.id}: $e');
        }
      }

      debugPrint('🎯 Final result: Loaded ${songs.length} songs from existing Firestore');
      return songs;
    } catch (e) {
      debugPrint('🚨 Error fetching songs: $e');
      rethrow;
    }
  }

  /// Adapt a single song document to our Song model
  Future<Song?> _adaptSongDocument(DocumentSnapshot doc) async {
    try {
      final data = doc.data() as Map<String, dynamic>? ?? {};
      debugPrint('🔧 Adapting song ${doc.id} with data keys: ${data.keys.toList()}');
      
      // Try different possible field names from your existing structure
      final title = _getFieldValue(data, ['title', 'name', 'song_title', 'titre']) ?? '';
      final artist = _getFieldValue(data, ['artist', 'author', 'artiste']) ?? AppConstants.defaultArtist;
      
      debugPrint('📝 Extracted: title="$title", artist="$artist"');
      
      // Handle song number - try different formats
      int? songNumber;
      final numberFields = ['number', 'song_number', 'num', 'id', 'songId'];
      for (final field in numberFields) {
        if (data.containsKey(field)) {
          final value = data[field];
          if (value is int) {
            songNumber = value;
            break;
          } else if (value is String) {
            songNumber = int.tryParse(value);
            if (songNumber != null) break;
          }
        }
      }
      
      // If no number found, try to extract from document ID
      songNumber ??= int.tryParse(doc.id);
      
      // Get duration if available
      int? duration;
      final durationValue = _getFieldValue(data, ['duration', 'length', 'duree']);
      if (durationValue is int) {
        duration = durationValue;
      } else if (durationValue is String) {
        duration = int.tryParse(durationValue);
      }

    // Get audio URL from your existing structure or Firebase Storage
    String audioUrl = '';
    final existingUrl = _getFieldValue(data, ['url', 'audio_url', 'audioUrl', 'audio', 'file_url']);
    
    if (existingUrl != null && existingUrl.toString().isNotEmpty) {
      // Convert cloud.google.com URLs to proper Firebase download URLs
      final urlString = existingUrl.toString();
      if (urlString.contains('storage.cloud.google.com')) {
        try {
          // Extract the path from the cloud.google.com URL and get proper download URL
          final uri = Uri.parse(urlString);
          final pathSegments = uri.pathSegments;
          if (pathSegments.length >= 3) {
            // Path format: /niseamen.appspot.com/music/418%20Cantique%20ECC.mp3
            final bucketName = pathSegments[0]; // niseamen.appspot.com
            final filePath = pathSegments.sublist(1).join('/'); // music/418%20Cantique%20ECC.mp3
            final decodedPath = Uri.decodeComponent(filePath); // music/418 Cantique ECC.mp3
            
            // Get proper download URL using Firebase Storage
            audioUrl = await _storage.getAudioUrl(decodedPath);
          } else {
            // Fallback: try to get by song ID
            audioUrl = await _storage.getAudioUrlBySongId(doc.id);
          }
        } catch (e) {
          debugPrint('Error converting cloud URL for song ${doc.id}: $e');
          // Fallback to direct URL if conversion fails
          audioUrl = urlString;
        }
      } else {
        // Use the URL as-is if it's not a cloud.google.com URL
        audioUrl = urlString;
      }
    } else {
      // Try to get from Firebase Storage using song ID or number
      try {
        audioUrl = await _storage.getAudioUrlBySongId(doc.id);
      } catch (e) {
        if (songNumber != null) {
          try {
            audioUrl = await _storage.getAudioUrlBySongId(songNumber.toString());
          } catch (e2) {
            debugPrint('Could not find audio for song ${doc.id}: $e2');
          }
        }
      }
    }

      // Get image URL
      final imageUrl = _getFieldValue(data, ['image', 'image_url', 'imageUrl', 'thumbnail', 'cover']);

      // Fetch lyrics from your existing structure
      final lyrics = await _fetchLyricsForSong(doc.id, data);

      return Song(
        id: doc.id,
        title: title,
        artist: artist,
        imageUrl: imageUrl?.toString() ?? AppConstants.defaultAlbumArt,
        audioUrl: audioUrl,
        duration: duration,
        lyrics: lyrics,
        songNumber: songNumber,
        createdAt: _parseTimestamp(data['created_at'] ?? data['createdAt']),
        updatedAt: _parseTimestamp(data['updated_at'] ?? data['updatedAt']),
      );
    } catch (e) {
      debugPrint('Error adapting song document ${doc.id}: $e');
      return null;
    }
  }

  /// Fetch lyrics for a song from your existing structure
  Future<Map<String, String>> _fetchLyricsForSong(String songId, Map<String, dynamic> songData) async {
    final lyrics = <String, String>{};

    try {
      // Option 1: Lyrics embedded in song document
      if (songData.containsKey('lyrics')) {
        final lyricsData = songData['lyrics'];
        if (lyricsData is Map) {
          // Multiple languages in one document
          for (final entry in lyricsData.entries) {
            final language = _mapLanguageKey(entry.key);
            if (entry.value is String && entry.value.isNotEmpty) {
              lyrics[language] = entry.value;
            }
          }
        } else if (lyricsData is String && lyricsData.isNotEmpty) {
          // Single language lyrics
          lyrics['goun'] = lyricsData;
        }
      }

      // Option 2: Separate lyrics collections (as per PRD)
      if (lyrics.isEmpty) {
        // Try Goun lyrics
        try {
          final gounDoc = await _firestore.collection('lyrics').doc(songId).get();
          if (gounDoc.exists) {
            final text = _getFieldValue(gounDoc.data(), ['text', 'lyrics', 'content']);
            if (text != null && text.isNotEmpty) {
              lyrics['goun'] = text;
            }
          }
        } catch (e) {
          debugPrint('Error fetching Goun lyrics for $songId: $e');
        }

        // Try French lyrics
        try {
          final frenchDoc = await _firestore.collection('lyrics_francais').doc(songId).get();
          if (frenchDoc.exists) {
            final text = _getFieldValue(frenchDoc.data(), ['text', 'lyrics', 'content']);
            if (text != null && text.isNotEmpty) {
              lyrics['french'] = text;
            }
          }
        } catch (e) {
          debugPrint('Error fetching French lyrics for $songId: $e');
        }
      }

      // Option 3: Try alternative collection names
      if (lyrics.isEmpty) {
        final alternativeCollections = [
          'song_lyrics',
          'hymn_lyrics', 
          'cantique_lyrics',
          'paroles'
        ];

        for (final collection in alternativeCollections) {
          try {
            final doc = await _firestore.collection(collection).doc(songId).get();
            if (doc.exists) {
              final data = doc.data() ?? {};
              
              // Check for different language fields
              final gounText = _getFieldValue(data, ['goun', 'goun_text', 'original']);
              final frenchText = _getFieldValue(data, ['french', 'francais', 'french_text']);
              final generalText = _getFieldValue(data, ['text', 'lyrics', 'content']);

              if (gounText != null && gounText.isNotEmpty) {
                lyrics['goun'] = gounText;
              }
              if (frenchText != null && frenchText.isNotEmpty) {
                lyrics['french'] = frenchText;
              }
              if (generalText != null && generalText.isNotEmpty && lyrics.isEmpty) {
                lyrics['goun'] = generalText; // Default to Goun if language not specified
              }

              if (lyrics.isNotEmpty) break; // Found lyrics, stop searching
            }
          } catch (e) {
            // Continue to next collection
          }
        }
      }
    } catch (e) {
      debugPrint('Error fetching lyrics for song $songId: $e');
    }

    return lyrics;
  }

  /// Get monthly program from your existing structure
  Future<MonthProgram?> getMonthProgram(int month, int year) async {
    try {
      // Try different possible document ID formats
      final possibleIds = [
        '${_getMonthName(month).toLowerCase()}_$year',
        '${month.toString().padLeft(2, '0')}_$year',
        '$year-${month.toString().padLeft(2, '0')}',
        '$year$month',
      ];

      // Try different collection names
      final possibleCollections = [
        'cantique_program',
        'programs',
        'monthly_programs',
        'song_programs',
        'hymn_programs'
      ];

      for (final collection in possibleCollections) {
        for (final docId in possibleIds) {
          try {
            final doc = await _firestore.collection(collection).doc(docId).get();
            if (doc.exists) {
              return _adaptProgramDocument(doc, month, year);
            }
          } catch (e) {
            // Continue trying other combinations
          }
        }
      }

      debugPrint('No program found for $month/$year');
      return null;
    } catch (e) {
      debugPrint('Error fetching month program: $e');
      return null;
    }
  }

  /// Adapt program document to our MonthProgram model
  MonthProgram? _adaptProgramDocument(DocumentSnapshot doc, int month, int year) {
    try {
      final data = doc.data() as Map<String, dynamic>? ?? {};
      
      // Try to find weeks data in different possible structures
      List<dynamic> weeksData = [];
      
      // Option 1: Direct weeks array
      if (data.containsKey('weeks')) {
        weeksData = data['weeks'] as List<dynamic>? ?? [];
      }
      // Option 2: Days structure
      else if (data.containsKey('days')) {
        // Convert daily structure to weekly structure
        // This would need custom logic based on your structure
      }
      // Option 3: Direct song list (convert to weeks)
      else if (data.containsKey('songs')) {
        final songs = data['songs'] as List<dynamic>? ?? [];
        if (songs.length >= 7) {
          // Assume first 7 songs are week 1, next 7 are week 2, etc.
          weeksData = _convertSongsToWeeks(songs, month, year);
        }
      }

      if (weeksData.isEmpty) {
        debugPrint('No weeks data found in program document ${doc.id}');
        return null;
      }

      return MonthProgram.fromFirestore({'weeks': weeksData}, doc.id);
    } catch (e) {
      debugPrint('Error adapting program document ${doc.id}: $e');
      return null;
    }
  }

  /// Helper methods

  /// Get field value with multiple possible field names
  dynamic _getFieldValue(Map<String, dynamic>? data, List<String> possibleKeys) {
    if (data == null) return null;
    
    for (final key in possibleKeys) {
      if (data.containsKey(key) && data[key] != null) {
        return data[key];
      }
    }
    return null;
  }

  /// Map different language keys to standard keys
  String _mapLanguageKey(String key) {
    final normalizedKey = key.toLowerCase();
    
    if (normalizedKey.contains('goun') || normalizedKey.contains('original')) {
      return 'goun';
    } else if (normalizedKey.contains('french') || normalizedKey.contains('francais')) {
      return 'french';
    } else if (normalizedKey.contains('english')) {
      return 'english';
    }
    
    return normalizedKey; // Return as-is if no mapping found
  }

  /// Parse timestamp from various formats
  DateTime? _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) return null;
    
    if (timestamp is Timestamp) {
      return timestamp.toDate();
    } else if (timestamp is int) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } else if (timestamp is String) {
      return DateTime.tryParse(timestamp);
    }
    
    return null;
  }

  /// Get month name
  String _getMonthName(int month) {
    const monthNames = [
      '', 'january', 'february', 'march', 'april', 'may', 'june',
      'july', 'august', 'september', 'october', 'november', 'december'
    ];
    return monthNames[month];
  }

  /// Convert song list to weeks structure (if needed)
  List<Map<String, dynamic>> _convertSongsToWeeks(List<dynamic> songs, int month, int year) {
    final weeks = <Map<String, dynamic>>[];
    final firstDay = DateTime(year, month, 1);
    
    // Calculate first Monday of the month
    DateTime monday = firstDay;
    while (monday.weekday != DateTime.monday) {
      monday = monday.add(const Duration(days: 1));
    }

    int songIndex = 0;
    int weekNumber = 1;
    
    while (songIndex < songs.length && monday.month == month) {
      final weekSongs = <int>[];
      
      // Get 7 songs for this week
      for (int i = 0; i < 7 && songIndex < songs.length; i++) {
        final songId = songs[songIndex];
        if (songId is int) {
          weekSongs.add(songId);
        } else if (songId is String) {
          final intId = int.tryParse(songId);
          if (intId != null) {
            weekSongs.add(intId);
          }
        }
        songIndex++;
      }

      if (weekSongs.length == 7) {
        weeks.add({
          'weekNumber': weekNumber,
          'date': monday.toIso8601String(),
          'songs': weekSongs,
        });
      }

      monday = monday.add(const Duration(days: 7));
      weekNumber++;
    }

    return weeks;
  }

  /// Test connection to your existing database
  Future<Map<String, dynamic>> testDatabaseStructure() async {
    final result = <String, dynamic>{};
    
    try {
      debugPrint('🧪 Testing database structure...');
      
      // Test songs collection
      debugPrint('🎵 Testing songs collection...');
      final songsSnapshot = await _firestore.collection('songs').limit(5).get();
      result['songs_collection_exists'] = songsSnapshot.docs.isNotEmpty;
      result['songs_count'] = songsSnapshot.docs.length;
      
      if (songsSnapshot.docs.isNotEmpty) {
        final sampleSong = songsSnapshot.docs.first.data();
        result['sample_song_fields'] = sampleSong.keys.toList();
        result['sample_song_data'] = sampleSong;
        result['sample_song_id'] = songsSnapshot.docs.first.id;
        
        debugPrint('📝 Sample song fields: ${sampleSong.keys.toList()}');
        debugPrint('📄 Sample song data: $sampleSong');
        
        // Test all songs count
        final allSongsSnapshot = await _firestore.collection('songs').count().get();
        result['total_songs_count'] = allSongsSnapshot.count;
        debugPrint('📊 Total songs in collection: ${allSongsSnapshot.count}');
      }

      // Test lyrics collections
      debugPrint('📝 Testing lyrics collection...');
      final lyricsSnapshot = await _firestore.collection('lyrics').limit(1).get();
      result['lyrics_collection_exists'] = lyricsSnapshot.docs.isNotEmpty;
      if (lyricsSnapshot.docs.isNotEmpty) {
        result['sample_lyrics_data'] = lyricsSnapshot.docs.first.data();
      }

      debugPrint('🇫🇷 Testing lyrics_francais collection...');
      final frenchLyricsSnapshot = await _firestore.collection('lyrics_francais').limit(1).get();
      result['french_lyrics_collection_exists'] = frenchLyricsSnapshot.docs.isNotEmpty;
      if (frenchLyricsSnapshot.docs.isNotEmpty) {
        result['sample_french_lyrics_data'] = frenchLyricsSnapshot.docs.first.data();
      }

      // Test programs collection
      debugPrint('📅 Testing cantique_program collection...');
      final programsSnapshot = await _firestore.collection('cantique_program').limit(1).get();
      result['programs_collection_exists'] = programsSnapshot.docs.isNotEmpty;
      
      if (programsSnapshot.docs.isNotEmpty) {
        final sampleProgram = programsSnapshot.docs.first.data();
        result['sample_program_fields'] = sampleProgram.keys.toList();
        result['sample_program_data'] = sampleProgram;
        debugPrint('📅 Sample program: $sampleProgram');
      }

      // Test users collection
      debugPrint('👤 Testing users collection...');
      final usersSnapshot = await _firestore.collection('users').limit(1).get();
      result['users_collection_exists'] = usersSnapshot.docs.isNotEmpty;

      // Test lastPlayed collection
      debugPrint('⏯️ Testing lastPlayed collection...');
      final lastPlayedSnapshot = await _firestore.collection('lastPlayed').limit(1).get();
      result['lastPlayed_collection_exists'] = lastPlayedSnapshot.docs.isNotEmpty;

      debugPrint('✅ Database structure test completed');

    } catch (e) {
      debugPrint('🚨 Database test error: $e');
      result['error'] = e.toString();
    }

    return result;
  }
}
