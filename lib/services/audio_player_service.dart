import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/song_model.dart';
import '../constants/app_constants.dart';
import '../services/firestore_service.dart';
import '../services/user_service.dart';

/// Audio player service with background playback support
class AudioPlayerService extends BaseAudioHandler {
  static AudioPlayerService? _instance;
  static AudioPlayerService get instance => _instance ??= AudioPlayerService._();

  AudioPlayerService._() {
    _initialize();
  }

  final AudioPlayer _audioPlayer = AudioPlayer();
  final StreamController<PlayerState> _playerStateController = StreamController<PlayerState>.broadcast();
  final StreamController<Song?> _currentSongController = StreamController<Song?>.broadcast();
  final StreamController<Duration> _positionController = StreamController<Duration>.broadcast();
  final StreamController<Duration?> _durationController = StreamController<Duration?>.broadcast();
  final StreamController<bool> _shuffleController = StreamController<bool>.broadcast();
  final StreamController<LoopMode> _loopModeController = StreamController<LoopMode>.broadcast();

  Song? _currentSong;
  List<Song> _playlist = [];
  int _currentIndex = 0;
  bool _shuffleMode = false;
  LoopMode _loopMode = LoopMode.off;

  // Getters for streams
  Stream<PlayerState> get playerStateStream => _playerStateController.stream;
  Stream<Song?> get currentSongStream => _currentSongController.stream;
  Stream<Duration> get positionStream => _positionController.stream;
  Stream<Duration?> get durationStream => _durationController.stream;
  Stream<bool> get shuffleModeStream => _shuffleController.stream;
  Stream<LoopMode> get loopModeStream => _loopModeController.stream;

  // Getters for current state
  Song? get currentSong => _currentSong;
  List<Song> get playlist => List.unmodifiable(_playlist);
  int get currentIndex => _currentIndex;
  bool get isPlaying => _audioPlayer.playing;
  bool get isPaused => !_audioPlayer.playing && _audioPlayer.processingState != ProcessingState.idle;
  bool get shuffleMode => _shuffleMode;
  LoopMode get loopMode => _loopMode;
  Duration get position => _audioPlayer.position;
  Duration? get duration => _audioPlayer.duration;
  PlayerState get playerState => _audioPlayer.playerState;

  /// Initialize the audio player service
  Future<void> _initialize() async {
    try {
      // Initialize audio session
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration.music());

      // Listen to player state changes
      _audioPlayer.playerStateStream.listen(_playerStateController.add);
      _audioPlayer.positionStream.listen(_positionController.add);
      _audioPlayer.durationStream.listen(_durationController.add);

      // Handle playback completion
      _audioPlayer.playerStateStream.listen((state) {
        if (state.processingState == ProcessingState.completed) {
          _handlePlaybackCompletion();
        }
      });

      debugPrint('AudioPlayerService initialized');
    } catch (e) {
      debugPrint('Error initializing AudioPlayerService: $e');
    }
  }

  /// Play a single song
  Future<void> playSong(Song song) async {
    try {
      _currentSong = song;
      _playlist = [song];
      _currentIndex = 0;
      
      await _audioPlayer.setUrl(song.audioUrl);
      await _audioPlayer.play();
      
      _currentSongController.add(song);
      _updateMediaItem(song);
      
      // Track this song as recently played
      _trackRecentlyPlayed(song);
      
      debugPrint('Playing song: ${song.title}');
    } catch (e) {
      debugPrint('Error playing song: $e');
      rethrow;
    }
  }

  /// Play a playlist starting from a specific song
  Future<void> playPlaylist(List<Song> songs, {int startIndex = 0}) async {
    if (songs.isEmpty) return;
    
    try {
      _playlist = List.from(songs);
      _currentIndex = startIndex.clamp(0, songs.length - 1);
      _currentSong = _playlist[_currentIndex];
      
      await _audioPlayer.setUrl(_currentSong!.audioUrl);
      await _audioPlayer.play();
      
      _currentSongController.add(_currentSong);
      _updateMediaItem(_currentSong!);
      
      debugPrint('Playing playlist: ${songs.length} songs, starting at index $startIndex');
    } catch (e) {
      debugPrint('Error playing playlist: $e');
      rethrow;
    }
  }

  /// Pause playback
  Future<void> pause() async {
    await _audioPlayer.pause();
  }

  /// Resume playback
  Future<void> resume() async {
    await _audioPlayer.play();
  }

  /// Stop playback
  Future<void> stop() async {
    await _audioPlayer.stop();
    _currentSong = null;
    _currentSongController.add(null);
  }

  /// Play next song in playlist
  Future<void> playNext() async {
    if (_playlist.isEmpty) return;
    
    int nextIndex;
    if (_shuffleMode) {
      nextIndex = _getRandomIndex();
    } else {
      nextIndex = (_currentIndex + 1) % _playlist.length;
    }
    
    if (_loopMode == LoopMode.off && nextIndex == 0 && _currentIndex == _playlist.length - 1) {
      await stop();
      return;
    }
    
    _currentIndex = nextIndex;
    _currentSong = _playlist[_currentIndex];
    
    await _audioPlayer.setUrl(_currentSong!.audioUrl);
    await _audioPlayer.play();
    
    _currentSongController.add(_currentSong);
    _updateMediaItem(_currentSong!);
  }

  /// Play previous song in playlist
  Future<void> playPrevious() async {
    if (_playlist.isEmpty) return;
    
    // If we're more than 3 seconds into the song, restart current song
    if (_audioPlayer.position.inSeconds > 3) {
      await _audioPlayer.seek(Duration.zero);
      return;
    }
    
    int previousIndex;
    if (_shuffleMode) {
      previousIndex = _getRandomIndex();
    } else {
      previousIndex = (_currentIndex - 1) % _playlist.length;
      if (previousIndex < 0) previousIndex = _playlist.length - 1;
    }
    
    _currentIndex = previousIndex;
    _currentSong = _playlist[_currentIndex];
    
    await _audioPlayer.setUrl(_currentSong!.audioUrl);
    await _audioPlayer.play();
    
    _currentSongController.add(_currentSong);
    _updateMediaItem(_currentSong!);
  }

  /// Seek to a specific position
  Future<void> seek(Duration position) async {
    await _audioPlayer.seek(position);
  }

  /// Set shuffle mode
  Future<void> setShuffle(bool enabled) async {
    _shuffleMode = enabled;
    _shuffleController.add(enabled);
  }

  /// Set loop mode
  Future<void> setLoopMode(LoopMode mode) async {
    _loopMode = mode;
    await _audioPlayer.setLoopMode(mode);
    _loopModeController.add(mode);
  }

  /// Set volume (0.0 to 1.0)
  Future<void> setVolume(double volume) async {
    await _audioPlayer.setVolume(volume.clamp(0.0, 1.0));
  }

  /// Set playback speed (0.5 to 2.0)
  Future<void> setSpeed(double speed) async {
    await _audioPlayer.setSpeed(speed.clamp(0.5, 2.0));
  }

  /// Add song to current playlist
  void addToPlaylist(Song song) {
    _playlist.add(song);
  }

  /// Remove song from current playlist
  void removeFromPlaylist(int index) {
    if (index >= 0 && index < _playlist.length) {
      _playlist.removeAt(index);
      
      // Adjust current index if necessary
      if (index < _currentIndex) {
        _currentIndex--;
      } else if (index == _currentIndex && _currentIndex >= _playlist.length) {
        _currentIndex = _playlist.length - 1;
      }
    }
  }

  /// Clear playlist
  void clearPlaylist() {
    _playlist.clear();
    _currentIndex = 0;
  }

  /// Get random index for shuffle mode
  int _getRandomIndex() {
    if (_playlist.length <= 1) return 0;
    
    int randomIndex;
    do {
      randomIndex = DateTime.now().millisecondsSinceEpoch % _playlist.length;
    } while (randomIndex == _currentIndex);
    
    return randomIndex;
  }

  /// Track a song as recently played
  void _trackRecentlyPlayed(Song song) {
    debugPrint('Tracked as recently played: ${song.title} (ID: ${song.id})');
    
    // Update the user's recently played list in Firestore
    UserService.instance.addToRecentlyPlayed(song).catchError((error) {
      debugPrint('Error tracking recently played song: $error');
    });
  }

  /// Handle playback completion
  void _handlePlaybackCompletion() {
    if (_loopMode == LoopMode.one) {
      _audioPlayer.seek(Duration.zero);
      _audioPlayer.play();
    } else if (_loopMode == LoopMode.all || _currentIndex < _playlist.length - 1) {
      playNext();
    }
  }

  /// Update media item for system notification
  void _updateMediaItem(Song song) {
    mediaItem.add(MediaItem(
      id: song.id,
      album: AppConstants.defaultArtist,
      title: song.title,
      artist: song.artist,
      duration: song.duration != null ? Duration(seconds: song.duration!) : null,
      artUri: Uri.tryParse(song.imageUrl ?? AppConstants.defaultAlbumArt),
    ));
  }

  // AudioHandler implementations for background playback
  @override
  Future<void> play() => resume();

  @override
  Future<void> skipToNext() => playNext();

  @override
  Future<void> skipToPrevious() => playPrevious();

  @override
  Future<void> setShuffleMode(AudioServiceShuffleMode shuffleMode) async {
    final enabled = shuffleMode == AudioServiceShuffleMode.all;
    await setShuffle(enabled);
  }

  @override
  Future<void> setRepeatMode(AudioServiceRepeatMode repeatMode) async {
    switch (repeatMode) {
      case AudioServiceRepeatMode.none:
        await setLoopMode(LoopMode.off);
        break;
      case AudioServiceRepeatMode.one:
        await setLoopMode(LoopMode.one);
        break;
      case AudioServiceRepeatMode.all:
        await setLoopMode(LoopMode.all);
        break;
      case AudioServiceRepeatMode.group:
        await setLoopMode(LoopMode.all);
        break;
    }
  }

  /// Dispose the service
  Future<void> dispose() async {
    await _audioPlayer.dispose();
    await _playerStateController.close();
    await _currentSongController.close();
    await _positionController.close();
    await _durationController.close();
    await _shuffleController.close();
    await _loopModeController.close();
  }

  /// Get current playback state as a map for persistence
  Map<String, dynamic> getPlaybackState() {
    return {
      'currentSongId': _currentSong?.id,
      'position': _audioPlayer.position.inMilliseconds,
      'isPlaying': _audioPlayer.playing,
      'shuffleMode': _shuffleMode,
      'loopMode': _loopMode.index,
      'currentIndex': _currentIndex,
    };
  }

  /// Restore playback state from a map
  Future<void> restorePlaybackState(Map<String, dynamic> state, List<Song> availableSongs) async {
    try {
      final currentSongId = state['currentSongId'] as String?;
      final position = Duration(milliseconds: state['position'] as int? ?? 0);
      final wasPlaying = state['isPlaying'] as bool? ?? false;
      final shuffleMode = state['shuffleMode'] as bool? ?? false;
      final loopModeIndex = state['loopMode'] as int? ?? 0;
      final currentIndex = state['currentIndex'] as int? ?? 0;

      if (currentSongId != null) {
        final song = availableSongs.where((s) => s.id == currentSongId).firstOrNull;
        if (song != null) {
          await playSong(song);
          await seek(position);
          
          if (!wasPlaying) {
            await pause();
          }
        }
      }

      await setShuffle(shuffleMode);
      await setLoopMode(LoopMode.values[loopModeIndex.clamp(0, LoopMode.values.length - 1)]);
    } catch (e) {
      debugPrint('Error restoring playback state: $e');
    }
  }
}
