import 'package:cloud_firestore/cloud_firestore.dart';

class WeekProgramModel {
  final int weekNumber;
  final DateTime startDate;
  final List<int> songs;

  const WeekProgramModel({
    required this.weekNumber,
    required this.startDate,
    required this.songs,
  });

  factory WeekProgramModel.fromJson(Map<String, dynamic> json) {
    DateTime startDate;
    if (json['startDate'] is String) {
      startDate = DateTime.parse(json['startDate']);
    } else if (json['startDate'] is Timestamp) {
      startDate = (json['startDate'] as Timestamp).toDate();
    } else {
      startDate = DateTime.now();
    }

    return WeekProgramModel(
      weekNumber: json['weekNumber'] ?? 1,
      startDate: startDate,
      songs: List<int>.from(json['songs'] ?? []),
    );
  }

  factory WeekProgramModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Handle Firestore Timestamp conversion
    DateTime startDate;
    if (data['startDate'] is Timestamp) {
      startDate = (data['startDate'] as Timestamp).toDate();
    } else if (data['startDate'] is String) {
      startDate = DateTime.parse(data['startDate']);
    } else {
      startDate = DateTime.now();
    }

    return WeekProgramModel(
      weekNumber: data['weekNumber'] ?? 1,
      startDate: startDate,
      songs: List<int>.from(data['songs'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'weekNumber': weekNumber,
      'startDate': startDate.toIso8601String(),
      'songs': songs,
    };
  }

  /// Check if this week contains the given date
  bool containsDate(DateTime date) {
    final endDate = startDate.add(const Duration(days: 6));
    return date.isAfter(startDate.subtract(const Duration(days: 1))) &&
        date.isBefore(endDate.add(const Duration(days: 1)));
  }

  /// Get song ID for a specific day of the week (0=Monday, 6=Sunday)
  int? getSongForDay(int dayIndex) {
    if (dayIndex < 0 || dayIndex >= songs.length) return null;
    return songs[dayIndex];
  }

  /// Check if this week program is valid
  bool get isValid {
    if (weekNumber < 1 || weekNumber > 5) return false;
    if (songs.isEmpty) return false;
    return true;
  }
}

class MonthProgramModel {
  final int year;
  final int month;
  final List<WeekProgramModel> weeks;

  const MonthProgramModel({
    required this.year,
    required this.month,
    required this.weeks,
  });

  factory MonthProgramModel.fromJson(Map<String, dynamic> json) {
    final weeksData = json['weeks'] as List<dynamic>? ?? [];
    final weeks = weeksData.map((weekJson) {
      if (weekJson is Map<String, dynamic>) {
        return WeekProgramModel.fromJson(weekJson);
      }
      return null;
    }).whereType<WeekProgramModel>().toList();

    return MonthProgramModel(
      year: json['year'] ?? DateTime.now().year,
      month: json['month'] ?? DateTime.now().month,
      weeks: weeks,
    );
  }

  factory MonthProgramModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Convert weeks array from Firestore format
    final weeks = <WeekProgramModel>[];
    if (data['weeks'] is List) {
      for (var i = 0; i < (data['weeks'] as List).length; i++) {
        final weekData = data['weeks'][i] as Map<String, dynamic>;

        // Parse date string from "DD-MM-YYYY" format
        DateTime startDate;
        if (weekData['date'] is String) {
          final parts = (weekData['date'] as String).split('-');
          if (parts.length == 3) {
            final day = int.parse(parts[0]);
            final month = int.parse(parts[1]);
            final year = int.parse(parts[2]);

            // Create Sunday date (mass day)
            final massSunday = DateTime.utc(year, month, day);

            // Get Monday by subtracting 6 days
            startDate = DateTime.utc(year, month, day - 6);
          } else {
            startDate = DateTime.now();
          }
        } else {
          startDate = DateTime.now();
        }

        weeks.add(WeekProgramModel(
          weekNumber: i + 1,
          startDate: startDate,
          songs: List<int>.from(weekData['songs'] ?? []),
        ));
      }
    }

    return MonthProgramModel(
      year: data['year'] ?? DateTime.now().year,
      month: data['month'] ?? DateTime.now().month,
      weeks: weeks,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'year': year,
      'month': month,
      'weeks': weeks.map((week) => week.toJson()).toList(),
    };
  }
}
