// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserModelAdapter extends TypeAdapter<UserModel> {
  @override
  final int typeId = 3;

  @override
  UserModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserModel(
      uid: fields[0] as String,
      email: fields[1] as String?,
      displayName: fields[2] as String?,
      photoURL: fields[3] as String?,
      emailVerified: fields[4] as bool,
      createdAt: fields[5] as DateTime?,
      lastSignInTime: fields[6] as DateTime?,
      providerIds: (fields[7] as List).cast<String>(),
      lastPlayedSongId: fields[8] as String?,
      preferredLanguage: fields[9] as String,
      recentlyPlayed: (fields[10] as List).cast<String>(),
      favorites: (fields[11] as List).cast<String>(),
      preferences: (fields[12] as Map).cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, UserModel obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.uid)
      ..writeByte(1)
      ..write(obj.email)
      ..writeByte(2)
      ..write(obj.displayName)
      ..writeByte(3)
      ..write(obj.photoURL)
      ..writeByte(4)
      ..write(obj.emailVerified)
      ..writeByte(5)
      ..write(obj.createdAt)
      ..writeByte(6)
      ..write(obj.lastSignInTime)
      ..writeByte(7)
      ..write(obj.providerIds)
      ..writeByte(8)
      ..write(obj.lastPlayedSongId)
      ..writeByte(9)
      ..write(obj.preferredLanguage)
      ..writeByte(10)
      ..write(obj.recentlyPlayed)
      ..writeByte(11)
      ..write(obj.favorites)
      ..writeByte(12)
      ..write(obj.preferences);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
      uid: json['uid'] as String,
      email: json['email'] as String?,
      displayName: json['displayName'] as String?,
      photoURL: json['photoURL'] as String?,
      emailVerified: json['emailVerified'] as bool,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      lastSignInTime: json['lastSignInTime'] == null
          ? null
          : DateTime.parse(json['lastSignInTime'] as String),
      providerIds: (json['providerIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      lastPlayedSongId: json['lastPlayedSongId'] as String?,
      preferredLanguage:
          json['preferredLanguage'] as String? ?? AppConstants.gounLanguage,
      recentlyPlayed: (json['recentlyPlayed'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      favorites: (json['favorites'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      preferences: json['preferences'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      'uid': instance.uid,
      'email': instance.email,
      'displayName': instance.displayName,
      'photoURL': instance.photoURL,
      'emailVerified': instance.emailVerified,
      'createdAt': instance.createdAt?.toIso8601String(),
      'lastSignInTime': instance.lastSignInTime?.toIso8601String(),
      'providerIds': instance.providerIds,
      'lastPlayedSongId': instance.lastPlayedSongId,
      'preferredLanguage': instance.preferredLanguage,
      'recentlyPlayed': instance.recentlyPlayed,
      'favorites': instance.favorites,
      'preferences': instance.preferences,
    };
