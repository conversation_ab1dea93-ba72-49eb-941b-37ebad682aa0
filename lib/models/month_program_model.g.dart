// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'month_program_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MonthProgramAdapter extends TypeAdapter<MonthProgram> {
  @override
  final int typeId = 2;

  @override
  MonthProgram read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MonthProgram(
      month: fields[0] as int,
      year: fields[1] as int,
      weeks: (fields[2] as List).cast<WeekProgramModel>(),
      createdAt: fields[3] as DateTime?,
      updatedAt: fields[4] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, MonthProgram obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.month)
      ..writeByte(1)
      ..write(obj.year)
      ..writeByte(2)
      ..write(obj.weeks)
      ..writeByte(3)
      ..write(obj.createdAt)
      ..writeByte(4)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MonthProgramAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MonthProgram _$MonthProgramFromJson(Map<String, dynamic> json) => MonthProgram(
      month: (json['month'] as num).toInt(),
      year: (json['year'] as num).toInt(),
      weeks: (json['weeks'] as List<dynamic>)
          .map((e) => WeekProgramModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$MonthProgramToJson(MonthProgram instance) =>
    <String, dynamic>{
      'month': instance.month,
      'year': instance.year,
      'weeks': instance.weeks,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };
