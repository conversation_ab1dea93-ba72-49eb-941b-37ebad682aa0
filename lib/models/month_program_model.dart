import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';
import 'week_program_model.dart';

part 'month_program_model.g.dart';

@JsonSerializable()
@HiveType(typeId: 2)
class MonthProgram {
  @HiveField(0)
  final int month; // 1-12

  @HiveField(1)
  final int year;

  @HiveField(2)
  final List<WeekProgramModel> weeks;

  @HiveField(3)
  final DateTime? createdAt;

  @HiveField(4)
  final DateTime? updatedAt;

  const MonthProgram({
    required this.month,
    required this.year,
    required this.weeks,
    this.createdAt,
    this.updatedAt,
  });

  factory MonthProgram.fromJson(Map<String, dynamic> json) => _$MonthProgramFromJson(json);
  Map<String, dynamic> toJson() => _$MonthProgramToJson(this);

  MonthProgram copyWith({
    int? month,
    int? year,
    List<WeekProgramModel>? weeks,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MonthProgram(
      month: month ?? this.month,
      year: year ?? this.year,
      weeks: weeks ?? this.weeks,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MonthProgram &&
          runtimeType == other.runtimeType &&
          month == other.month &&
          year == other.year;

  @override
  int get hashCode => month.hashCode ^ year.hashCode;

  @override
  String toString() {
    return 'MonthProgram{month: $month, year: $year, weeks: ${weeks.length}}';
  }

  /// Get the document ID for Firestore (format: {month}_{year})
  String get documentId {
    final monthNames = [
      '', 'january', 'february', 'march', 'april', 'may', 'june',
      'july', 'august', 'september', 'october', 'november', 'december'
    ];
    return '${monthNames[month]}_$year';
  }

  /// Get formatted month name
  String get monthName {
    const monthNames = [
      '', 'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return monthNames[month];
  }

  /// Get week program that contains the given date
  WeekProgramModel? getWeekForDate(DateTime date) {
    for (final week in weeks) {
      if (week.containsDate(date)) {
        return week;
      }
    }
    return null;
  }

  /// Get song ID for a specific date
  int? getSongForDate(DateTime date) {
    final week = getWeekForDate(date);
    if (week == null) return null;

    // Convert date to day of week index (0=Monday, 6=Sunday)
    final dayOfWeekIndex = date.weekday == 7 ? 0 : date.weekday;
    return week.getSongForDay(dayOfWeekIndex - 1);
  }

  /// Get the first day of the month
  DateTime get firstDay {
    return DateTime(year, month, 1);
  }

  /// Get the last day of the month
  DateTime get lastDay {
    return DateTime(year, month + 1, 0);
  }

  /// Check if this month program is current (contains today's date)
  bool get isCurrent {
    final now = DateTime.now();
    return now.year == year && now.month == month;
  }

  /// Check if this month program is valid
  bool get isValid {
    if (month < 1 || month > 12) return false;
    if (year < 2000 || year > 2100) return false;
    if (weeks.isEmpty) return false;
    
    // Check that all weeks are valid
    return weeks.every((week) => week.isValid);
  }

  /// Get total number of unique songs in this month
  int get totalUniqueSongs {
    final allSongs = <int>{};
    for (final week in weeks) {
      allSongs.addAll(week.songs);
    }
    return allSongs.length;
  }

  /// Get all songs for the month (with duplicates)
  List<int> get allSongs {
    final songs = <int>[];
    for (final week in weeks) {
      songs.addAll(week.songs);
    }
    return songs;
  }

  /// Get week by week number
  WeekProgramModel? getWeekByNumber(int weekNumber) {
    try {
      return weeks.firstWhere((week) => week.weekNumber == weekNumber);
    } catch (e) {
      return null;
    }
  }

  /// Factory method to create from Firestore data
  factory MonthProgram.fromFirestore(Map<String, dynamic> data, String documentId) {
    // Parse month and year from document ID
    final parts = documentId.split('_');
    if (parts.length != 2) {
      throw ArgumentError('Invalid document ID format: $documentId');
    }

    final monthNames = {
      'january': 1, 'february': 2, 'march': 3, 'april': 4,
      'may': 5, 'june': 6, 'july': 7, 'august': 8,
      'september': 9, 'october': 10, 'november': 11, 'december': 12
    };

    final month = monthNames[parts[0].toLowerCase()];
    final year = int.tryParse(parts[1]);

    if (month == null || year == null) {
      throw ArgumentError('Invalid document ID format: $documentId');
    }

    // Parse weeks data
    final weeksList = (data['weeks'] as List?) ?? [];
    final weeks = weeksList.map((weekData) {
      final weekMap = weekData as Map<String, dynamic>;
      return WeekProgramModel(
        weekNumber: weekMap['weekNumber'] ?? 0,
        startDate: DateTime.parse(weekMap['date'] ?? DateTime.now().toIso8601String()),
        songs: List<int>.from(weekMap['songs'] ?? []),
      );
    }).toList();

    return MonthProgram(
      month: month,
      year: year,
      weeks: weeks,
    );
  }
}
