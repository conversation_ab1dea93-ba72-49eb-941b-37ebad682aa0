import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'song_model.g.dart';

@JsonSerializable()
@HiveType(typeId: 0)
class Song {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String artist;

  @HiveField(3)
  final String? imageUrl;

  @HiveField(4)
  final String audioUrl;

  @HiveField(5)
  final int? duration; // in seconds

  @HiveField(6)
  final Map<String, String> lyrics; // language -> text

  @HiveField(7)
  final int? songNumber;

  @HiveField(8)
  final DateTime? createdAt;

  @HiveField(9)
  final DateTime? updatedAt;

  const Song({
    required this.id,
    required this.title,
    required this.artist,
    this.imageUrl,
    required this.audioUrl,
    this.duration,
    required this.lyrics,
    this.songNumber,
    this.createdAt,
    this.updatedAt,
  });

  factory Song.fromJson(Map<String, dynamic> json) => _$SongFrom<PERSON>son(json);
  Map<String, dynamic> toJson() => _$SongTo<PERSON>son(this);

  Song copyWith({
    String? id,
    String? title,
    String? artist,
    String? imageUrl,
    String? audioUrl,
    int? duration,
    Map<String, String>? lyrics,
    int? songNumber,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Song(
      id: id ?? this.id,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      imageUrl: imageUrl ?? this.imageUrl,
      audioUrl: audioUrl ?? this.audioUrl,
      duration: duration ?? this.duration,
      lyrics: lyrics ?? this.lyrics,
      songNumber: songNumber ?? this.songNumber,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Song &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          title == other.title &&
          artist == other.artist &&
          imageUrl == other.imageUrl &&
          audioUrl == other.audioUrl &&
          duration == other.duration &&
          songNumber == other.songNumber;

  @override
  int get hashCode =>
      id.hashCode ^
      title.hashCode ^
      artist.hashCode ^
      imageUrl.hashCode ^
      audioUrl.hashCode ^
      duration.hashCode ^
      songNumber.hashCode;

  @override
  String toString() {
    return 'Song{id: $id, title: $title, artist: $artist, songNumber: $songNumber}';
  }

  /// Get lyrics for a specific language
  String getLyrics(String language) {
    return lyrics[language] ?? lyrics['goun'] ?? lyrics.values.first;
  }

  /// Check if lyrics are available for a specific language
  bool hasLyrics(String language) {
    return lyrics.containsKey(language) && lyrics[language]!.isNotEmpty;
  }

  /// Get available languages for lyrics
  List<String> get availableLanguages {
    return lyrics.keys.where((key) => lyrics[key]!.isNotEmpty).toList();
  }

  /// Get formatted duration string (mm:ss)
  String get formattedDuration {
    if (duration == null) return '--:--';
    
    final minutes = duration! ~/ 60;
    final seconds = duration! % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Check if song has a custom image or uses default
  bool get hasCustomImage {
    return imageUrl != null && imageUrl!.isNotEmpty;
  }
}