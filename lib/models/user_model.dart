import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';
import '../constants/app_constants.dart';

part 'user_model.g.dart';

/// User model class representing authenticated user data for NiseAmen
@JsonSerializable()
@HiveType(typeId: 3)
class UserModel {
  @HiveField(0)
  final String uid;

  @HiveField(1)
  final String? email;

  @HiveField(2)
  final String? displayName;

  @HiveField(3)
  final String? photoURL;

  @HiveField(4)
  final bool emailVerified;

  @HiveField(5)
  final DateTime? createdAt;

  @HiveField(6)
  final DateTime? lastSignInTime;

  @HiveField(7)
  final List<String> providerIds;

  @HiveField(8)
  final String? lastPlayedSongId;

  @HiveField(9)
  final String preferredLanguage;

  @HiveField(10)
  final List<String> recentlyPlayed;

  @HiveField(11)
  final List<String> favorites;

  @HiveField(12)
  final Map<String, dynamic> preferences;

  const UserModel({
    required this.uid,
    this.email,
    this.displayName,
    this.photoURL,
    required this.emailVerified,
    this.createdAt,
    this.lastSignInTime,
    this.providerIds = const [],
    this.lastPlayedSongId,
    this.preferredLanguage = AppConstants.gounLanguage,
    this.recentlyPlayed = const [],
    this.favorites = const [],
    this.preferences = const {},
  });

  /// Create UserModel from Firebase User
  factory UserModel.fromFirebaseUser(firebase_auth.User firebaseUser) {
    return UserModel(
      uid: firebaseUser.uid,
      email: firebaseUser.email,
      displayName: firebaseUser.displayName,
      photoURL: firebaseUser.photoURL,
      emailVerified: firebaseUser.emailVerified,
      createdAt: firebaseUser.metadata.creationTime,
      lastSignInTime: firebaseUser.metadata.lastSignInTime,
      providerIds: firebaseUser.providerData.map((info) => info.providerId).toList(),
    );
  }

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  /// Create UserModel from manual JSON (for backward compatibility)
  factory UserModel.fromJsonManual(Map<String, dynamic> json) {
    return UserModel(
      uid: json['uid'] as String,
      email: json['email'] as String?,
      displayName: json['displayName'] as String?,
      photoURL: json['photoURL'] as String?,
      emailVerified: json['emailVerified'] as bool? ?? false,
      createdAt: json['createdAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'] as int)
          : null,
      lastSignInTime: json['lastSignInTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['lastSignInTime'] as int)
          : null,
      providerIds: (json['providerIds'] as List<dynamic>?)?.cast<String>() ?? [],
      lastPlayedSongId: json['lastPlayedSongId'] as String?,
      preferredLanguage: json['preferredLanguage'] as String? ?? AppConstants.gounLanguage,
      recentlyPlayed: (json['recentlyPlayed'] as List<dynamic>?)?.cast<String>() ?? [],
      favorites: (json['favorites'] as List<dynamic>?)?.cast<String>() ?? [],
      preferences: json['preferences'] as Map<String, dynamic>? ?? {},
    );
  }

  /// Create a copy of UserModel with updated fields
  UserModel copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? photoURL,
    bool? emailVerified,
    DateTime? createdAt,
    DateTime? lastSignInTime,
    List<String>? providerIds,
    String? lastPlayedSongId,
    String? preferredLanguage,
    List<String>? recentlyPlayed,
    List<String>? favorites,
    Map<String, dynamic>? preferences,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      emailVerified: emailVerified ?? this.emailVerified,
      createdAt: createdAt ?? this.createdAt,
      lastSignInTime: lastSignInTime ?? this.lastSignInTime,
      providerIds: providerIds ?? this.providerIds,
      lastPlayedSongId: lastPlayedSongId ?? this.lastPlayedSongId,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      recentlyPlayed: recentlyPlayed ?? this.recentlyPlayed,
      favorites: favorites ?? this.favorites,
      preferences: preferences ?? this.preferences,
    );
  }

  /// Get user's first name from display name
  String? get firstName {
    if (displayName == null) return null;
    final parts = displayName!.split(' ');
    return parts.isNotEmpty ? parts.first : null;
  }

  /// Get user's last name from display name
  String? get lastName {
    if (displayName == null) return null;
    final parts = displayName!.split(' ');
    return parts.length > 1 ? parts.skip(1).join(' ') : null;
  }

  /// Get user's initials for avatar
  String get initials {
    if (displayName != null && displayName!.isNotEmpty) {
      final parts = displayName!.split(' ');
      if (parts.length >= 2) {
        return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
      } else {
        return displayName![0].toUpperCase();
      }
    } else if (email != null && email!.isNotEmpty) {
      return email![0].toUpperCase();
    }
    return 'U';
  }

  /// Check if user signed in with Google
  bool get isGoogleUser => providerIds.contains('google.com');

  /// Check if user signed in with Apple
  bool get isAppleUser => providerIds.contains('apple.com');

  /// Check if user signed in with email/password
  bool get isEmailUser => providerIds.contains('password');

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel &&
        other.uid == uid &&
        other.email == email &&
        other.displayName == displayName &&
        other.photoURL == photoURL &&
        other.emailVerified == emailVerified;
  }

  @override
  int get hashCode {
    return uid.hashCode ^
        email.hashCode ^
        displayName.hashCode ^
        photoURL.hashCode ^
        emailVerified.hashCode;
  }

  @override
  String toString() {
    return 'UserModel(uid: $uid, email: $email, displayName: $displayName, emailVerified: $emailVerified, preferredLanguage: $preferredLanguage)';
  }

  /// NiseAmen-specific methods

  /// Add song to recently played list
  UserModel addToRecentlyPlayed(String songId) {
    final newRecentlyPlayed = List<String>.from(recentlyPlayed);
    newRecentlyPlayed.remove(songId); // Remove if already exists
    newRecentlyPlayed.insert(0, songId); // Add to beginning
    
    // Keep only last 20 items
    if (newRecentlyPlayed.length > 20) {
      newRecentlyPlayed.removeRange(20, newRecentlyPlayed.length);
    }
    
    return copyWith(
      recentlyPlayed: newRecentlyPlayed,
      lastPlayedSongId: songId,
    );
  }

  /// Add song to favorites
  UserModel addToFavorites(String songId) {
    if (favorites.contains(songId)) return this;
    
    final newFavorites = List<String>.from(favorites)..add(songId);
    return copyWith(favorites: newFavorites);
  }

  /// Remove song from favorites
  UserModel removeFromFavorites(String songId) {
    final newFavorites = List<String>.from(favorites)..remove(songId);
    return copyWith(favorites: newFavorites);
  }

  /// Toggle favorite status of a song
  UserModel toggleFavorite(String songId) {
    return favorites.contains(songId) 
        ? removeFromFavorites(songId)
        : addToFavorites(songId);
  }

  /// Check if song is in favorites
  bool isFavorite(String songId) {
    return favorites.contains(songId);
  }

  /// Update preferred language
  UserModel updatePreferredLanguage(String language) {
    return copyWith(preferredLanguage: language);
  }

  /// Update a specific preference
  UserModel updatePreference(String key, dynamic value) {
    final newPreferences = Map<String, dynamic>.from(preferences);
    newPreferences[key] = value;
    return copyWith(preferences: newPreferences);
  }

  /// Get a specific preference with default value
  T getPreference<T>(String key, T defaultValue) {
    return preferences[key] as T? ?? defaultValue;
  }

  /// Get display name for the app
  String get displayNameOrEmail {
    return displayName ?? email ?? 'User';
  }

  /// Check if user has recently played songs
  bool get hasRecentlyPlayed {
    return recentlyPlayed.isNotEmpty;
  }

  /// Check if user has favorite songs
  bool get hasFavorites {
    return favorites.isNotEmpty;
  }

  /// Get the number of favorite songs
  int get favoriteCount {
    return favorites.length;
  }

  /// Get recently played songs count
  int get recentlyPlayedCount {
    return recentlyPlayed.length;
  }
}
