import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:audio_service/audio_service.dart';

import 'firebase_options.dart';
import 'services/cache_service.dart';
import 'services/program_service.dart';
import 'services/audio_player_service.dart';
import 'utils/timezone_utils.dart';
import 'constants/app_constants.dart';
import 'providers/theme_provider.dart';
import 'screens/main_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase with platform-specific options
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize NiseAmen services
  await TimezoneUtils.initialize();
  await CacheService.instance.initialize();
  await ProgramService.instance.initialize();

  // Initialize audio service for background playback
  await AudioService.init(
    builder: () => AudioPlayerService.instance,
    config: const AudioServiceConfig(
      androidNotificationChannelId: 'com.niseamen.app.audio',
      androidNotificationChannelName: 'NiseAmen Audio',
      androidNotificationChannelDescription: 'Audio playback for NiseAmen hymns',
      androidShowNotificationBadge: true,
    ),
  );

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(currentThemeModeProvider);
    final lightTheme = ref.watch(lightThemeProvider);
    final darkTheme = ref.watch(darkThemeProvider);

    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      theme: lightTheme,
      darkTheme: darkTheme,
      themeMode: themeMode,
      home: const MainScreen(),
    );
  }
}


