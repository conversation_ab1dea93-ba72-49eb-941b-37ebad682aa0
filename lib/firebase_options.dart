// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyC3zU2TFyASwkVHKlNFf57CJhp_9wnwRVU',
    appId: '1:578866985713:web:05df1380459293b50bc750',
    messagingSenderId: '578866985713',
    projectId: 'niseamen',
    authDomain: 'niseamen.firebaseapp.com',
    storageBucket: 'niseamen.appspot.com',
    databaseURL: 'https://niseamen.firebaseio.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCi5o4Jj-P2DrJc8XnOI_zQk5vAEOQ02uo',
    appId: '1:578866985713:android:6f406ae7bb049ead0bc750',
    messagingSenderId: '578866985713',
    projectId: 'niseamen',
    storageBucket: 'niseamen.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDEMO_IOS_API_KEY_REPLACE_WITH_YOUR_ACTUAL_KEY',
    appId: '1:578866985713:ios:demo_app_id_replace_with_actual',
    messagingSenderId: '578866985713',
    projectId: 'niseamen',
    storageBucket: 'niseamen.appspot.com',
    iosBundleId: 'com.isaacgounton.niseamen',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDEMO_MACOS_API_KEY_REPLACE_WITH_YOUR_ACTUAL_KEY',
    appId: '1:578866985713:ios:demo_app_id_replace_with_actual',
    messagingSenderId: '578866985713',
    projectId: 'niseamen',
    storageBucket: 'niseamen.appspot.com',
    iosBundleId: 'com.isaacgounton.niseamen',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyC3zU2TFyASwkVHKlNFf57CJhp_9wnwRVU',
    appId: '1:578866985713:web:05df1380459293b50bc750',
    messagingSenderId: '578866985713',
    projectId: 'niseamen',
    authDomain: 'niseamen.firebaseapp.com',
    storageBucket: 'niseamen.appspot.com',
    databaseURL: 'https://niseamen.firebaseio.com',
  );
}
