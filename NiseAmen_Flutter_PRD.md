# NiseAmen Flutter App - Product Requirements Document (PRD)

## Project Overview

**Project Name:** NiseAmen Mobile App (Flutter Version)  
**Target Platform:** Flutter (iOS & Android)  
**Purpose:** Digital hymn book for Église du christianisme céleste (Celestial Church of Christ)  
**Core Features:** Audio playback, multi-language lyrics, weekly/monthly programs, featured songs

## 1. Technical Architecture

### 1.1 Flutter Framework Requirements
- **Flutter SDK:** Latest stable version (3.x+)
- **Target Platforms:** iOS 12+, Android API Level 21+
- **Architecture Pattern:** Provider/Riverpod for state management
- **Navigation:** GoRouter for declarative routing
- **Audio:** just_audio package for audio playback
- **Storage:** shared_preferences + hive for local caching
- **HTTP:** dio package for API calls

### 1.2 State Management Structure

#### Core Stores to Implement:
1. **PlayerStore** - Audio playback management
2. **ProgramStore** - Weekly/monthly program data
3. **SongStore** - Song catalog and lyrics
4. **UserStore** - Authentication and preferences
5. **SearchStore** - Search functionality

## 2. Firebase Configuration

### 2.1 Firebase Project Setup
```
Project ID: niseamen
Auth Domain: niseamen.firebaseapp.com
Database URL: https://niseamen.firebaseio.com
Storage Bucket: niseamen.appspot.com
Messaging Sender ID: ************
App ID: 1:************:web:05df1380459293b50bc750
API Key: AIzaSyC3zU2TFyASwkVHKlNFf57CJhp_9wnwRVU
```

### 2.2 Required Firebase Services
- **Authentication:** Firebase Auth for user management
- **Firestore:** NoSQL database for songs, lyrics, and programs
- **Storage:** Firebase Storage for audio files (MP3)
- **Analytics:** Firebase Analytics for usage tracking

### 2.3 Flutter Firebase Packages
```yaml
dependencies:
  firebase_core: latest
  firebase_auth: latest
  cloud_firestore: latest
  firebase_storage: latest
  firebase_analytics: latest
```

## 3. Database Structure

### 3.1 Firestore Collections

#### Collection: `songs`
```javascript
{
  id: "1", // Document ID (string)
  title: "Cantique Title",
  artist: "Église du christianisme céleste",
  duration: 180, // Duration in seconds (optional)
  url: "music/001 Cantique ECC.mp3" // Firebase Storage path
}
```

#### Collection: `lyrics` (Goun language)
```javascript
{
  id: "1", // Matches song ID
  text: "Full lyrics text in Goun language..."
}
```

#### Collection: `lyrics_francais` (French language)
```javascript
{
  id: "1", // Matches song ID  
  text: "Full lyrics text in French language..."
}
```

#### Collection: `cantique_program` (Monthly Programs)
```javascript
{
  id: "march_2025", // Document ID format: {month}_{year}
  month: "march",
  weeks: [
    {
      date: "02-03-2025", // Sunday mass date (DD-MM-YYYY)
      songs: [1, 45, 123, 67, 234, 89, 156] // 7 songs (Mon-Sun)
    },
    {
      date: "09-03-2025",
      songs: [12, 78, 145, 23, 189, 67, 234]
    }
    // ... more weeks
  ]
}
```

### 3.2 Firebase Storage Structure
```
/music/
  ├── 001 Cantique ECC.mp3
  ├── 002 Cantique ECC.mp3
  └── ... (numbered song files)
```

## 4. Core Logic Implementation

### 4.1 Timezone Handling (Critical)
- **Primary Timezone:** GMT+1 (Africa/Lagos)
- **Week Calculation:** ISO weeks starting Monday
- **Featured Song Logic:** Changes daily at midnight GMT+1
- **Date Formatting:** All dates converted to GMT+1 for consistency

```dart
// Example timezone handling
DateTime getCurrentGMTPlus1() {
  return DateTime.now().toUtc().add(Duration(hours: 1));
}

int getDayOfWeekIndex(DateTime date) {
  // Convert Sunday=0 to Monday=0 indexing
  return date.weekday == 7 ? 0 : date.weekday;
}
```

### 4.2 Program Logic Flow

#### Weekly Program Algorithm:
1. Get current date in GMT+1
2. Calculate ISO week number
3. Fetch monthly program from Firestore
4. Find week containing current date
5. Return 7-song array (Monday=index 0, Sunday=index 6)

#### Featured Song Algorithm:
1. Get current date in GMT+1
2. Calculate day of week index (0=Monday, 6=Sunday)
3. Get current week's song array
4. Return song at dayOfWeekIndex
5. Cache result for entire day (GMT+1 midnight reset)

#### Fallback Strategy:
- If no program data: Generate deterministic songs using date-based hash
- If network fails: Use cached data even if expired
- Minimum 300+ songs should be available for variety

### 4.3 Caching Strategy

#### Cache Keys and Expiration:
```dart
static const String SONGS_CACHE = 'songs_cache';
static const String PROGRAM_CACHE = 'program_cache'; 
static const String FEATURED_SONG_CACHE = 'featured_song_cache';
static const Duration CACHE_DURATION = Duration(hours: 24);
```

#### Cache Implementation Pattern:
1. Check local cache first
2. Validate cache timestamp (24-hour expiration)
3. If invalid/missing: Fetch from Firebase
4. Store fresh data with timestamp
5. On network error: Use expired cache as fallback

## 5. Audio System Requirements

### 5.1 Audio Package Setup
```yaml
dependencies:
  just_audio: latest
  audio_service: latest  # For background playback
  audio_session: latest  # For audio focus management
```

### 5.2 Audio Features to Implement
- **Background Playback:** Continue playing when app backgrounded
- **Media Controls:** Lock screen controls, notification controls
- **Audio Focus:** Handle phone calls, notifications
- **Progress Tracking:** Real-time playback position
- **Queue Management:** Next/previous song navigation
- **Seeking:** Allow users to seek within songs

### 5.3 Audio URL Resolution
```dart
// Firebase Storage URL resolution
Future<String> getAudioUrl(String storagePath) async {
  final ref = FirebaseStorage.instance.ref(storagePath);
  return await ref.getDownloadURL();
}
```

## 6. UI/UX Requirements

### 6.1 App Navigation Structure
```
App/
├── Home (Tab 1)
│   ├── Featured Song Card
│   ├── Current Week Program  
│   └── Current Month Overview
├── Search (Tab 2)
│   ├── Song Search
│   └── Search Results
├── Profile (Tab 3)
│   ├── User Info
│   ├── Language Settings
│   └── Recently Played
├── Player (Modal)
│   ├── Album Art
│   ├── Song Info
│   ├── Playback Controls
│   ├── Progress Bar
│   └── Lyrics Display
└── Week Details (Push)
    └── Weekly Song List
```

### 6.2 Key UI Components
- **MiniPlayer:** Persistent bottom player bar
- **SongItem:** Reusable song list tile with play button
- **FeaturedSong:** Daily featured song highlight card
- **WeeklyProgram:** 7-day song grid display
- **PlayerControls:** Play/pause, skip, seek controls
- **LyricsDisplay:** Multi-language lyrics with toggle

### 6.3 Theme Requirements
- **Primary Colors:** Dark theme throughout
- **Typography:** Clear, readable fonts for religious content
- **Accessibility:** Support for screen readers, large text
- **Language Support:** Goun and French UI elements

## 7. Default Resources

### 7.1 Default Album Art
```
URL: https://filebrowser.etugrand.com/api/public/dl/3Brw2rx4/home/<USER>/CantiqueECC.webp
Purpose: Used for all songs as placeholder/default image
```

### 7.2 Fallback Artist Name
```
Default Artist: "Église du christianisme céleste"
```

### 7.3 Error Handling Messages
```dart
// Multilingual error messages
Map<String, String> errorMessages = {
  'goun': "No lyrics available",
  'french': "Pas de paroles disponibles", 
  'english': "Content not available"
};
```

## 8. Data Models

### 8.1 Song Model
```dart
class Song {
  final String id;
  final String title;
  final String artist;
  final String? imageUrl;
  final String audioUrl;
  final int? duration; // in seconds
  final Map<String, String> lyrics; // language -> text
  final int? songNumber;
  
  Song({
    required this.id,
    required this.title,
    required this.artist,
    this.imageUrl,
    required this.audioUrl,
    this.duration,
    required this.lyrics,
    this.songNumber,
  });
}
```

### 8.2 Program Models
```dart
class WeekProgram {
  final int weekNumber;
  final DateTime startDate; // Monday
  final List<int> songs; // 7 song IDs (Mon-Sun)
  
  WeekProgram({
    required this.weekNumber,
    required this.startDate,
    required this.songs,
  });
}

class MonthProgram {
  final int month;
  final int year;
  final List<WeekProgram> weeks;
  
  MonthProgram({
    required this.month,
    required this.year,
    required this.weeks,
  });
}
```

### 8.3 User Model
```dart
class User {
  final String id;
  final String displayName;
  final String email;
  final String? photoUrl;
  final String? lastPlayedSongId;
  final String preferredLanguage; // 'goun' or 'french'
  
  User({
    required this.id,
    required this.displayName,
    required this.email,
    this.photoUrl,
    this.lastPlayedSongId,
    this.preferredLanguage = 'goun',
  });
}
```

## 9. Performance Requirements

### 9.1 Loading Performance
- **Song Loading:** Parallel processing with 10-second timeout
- **Cache First:** Always check cache before network calls
- **Progressive Loading:** Show UI immediately, load data in background
- **Image Optimization:** Use cached_network_image package

### 9.2 Memory Management
- **Audio Cleanup:** Dispose audio players when not in use
- **Image Caching:** Limit cache size to prevent memory issues
- **List Virtualization:** Use ListView.builder for large song lists

### 9.3 Network Resilience
- **Retry Logic:** 3 attempts with exponential backoff
- **Offline Support:** Full app functionality with cached data
- **Graceful Degradation:** Show cached content on network errors

## 10. Development Setup Instructions

### 10.1 Environment Setup
1. Install Flutter SDK (latest stable)
2. Set up Firebase project with above configuration
3. Configure Firebase for Flutter (FlutterFire CLI)
4. Add platform-specific Firebase configuration files
5. Set up development certificates for iOS

### 10.2 Required Permissions

#### Android (android/app/src/main/AndroidManifest.xml)
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
```

#### iOS (ios/Runner/Info.plist)
```xml
<key>UIBackgroundModes</key>
<array>
    <string>audio</string>
</array>
```

### 10.3 Build Configuration
```yaml
# pubspec.yaml
name: niseamen_flutter
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
    
  # Firebase
  firebase_core: latest
  firebase_auth: latest
  cloud_firestore: latest
  firebase_storage: latest
  
  # Audio
  just_audio: latest
  audio_service: latest
  
  # State Management
  provider: latest # or riverpod
  
  # Storage
  shared_preferences: latest
  hive: latest
  hive_flutter: latest
  
  # UI
  cached_network_image: latest
  
  # Utilities
  dio: latest
  go_router: latest
```

## 11. Testing Strategy

### 11.1 Unit Testing
- **Audio Service:** Mock audio playback for tests
- **Program Logic:** Test timezone calculations
- **Cache Logic:** Test cache expiration and fallbacks
- **Date Calculations:** Test week/day calculations

### 11.2 Integration Testing
- **Firebase:** Test Firestore queries and caching
- **Audio Playback:** Test background playback
- **Navigation:** Test app flow and deep linking

### 11.3 Platform Testing
- **iOS:** Test audio session handling, background modes
- **Android:** Test foreground services, notification controls
- **Physical Devices:** Test actual audio playback (simulators limited)

## 12. Deployment Requirements

### 12.1 App Store Requirements
- **iOS:** Configure app icons, launch screens
- **Android:** Configure adaptive icons, splash screens
- **Metadata:** App description emphasizing religious/spiritual purpose

### 12.2 Build Configurations
```dart
// Different environments
abstract class Environment {
  static const String production = 'production';
  static const String development = 'development';
}
```

### 12.3 Release Process
1. Test on physical devices (iOS/Android)
2. Verify Firebase connection in production
3. Test background audio playback
4. Validate app store metadata
5. Submit for review with religious app category

## 13. Maintenance & Updates

### 13.1 Content Updates
- **Monthly Programs:** Update via Firebase console
- **Song Library:** Add new songs to Firestore + Storage
- **Lyrics Updates:** Modify lyrics collections as needed

### 13.2 Monitoring
- **Firebase Analytics:** Track usage patterns
- **Crashlytics:** Monitor app crashes
- **Performance:** Monitor audio loading times

### 13.3 Future Enhancements
- **Offline Download:** Allow downloading songs for offline play
- **Favorites:** User favorite songs functionality
- **Sharing:** Share songs or daily verses
- **Push Notifications:** Daily featured song notifications

---

## Developer Handoff Checklist

### ✅ Firebase Setup
- [ ] Create Firebase project with provided configuration
- [ ] Enable Authentication, Firestore, Storage, Analytics
- [ ] Configure security rules for read access
- [ ] Upload initial song library to Storage

### ✅ Data Migration
- [ ] Import existing songs from React Native app
- [ ] Migrate lyrics from both language collections
- [ ] Import current monthly programs
- [ ] Verify data integrity and relationships

### ✅ Core Implementation
- [ ] Set up Flutter project with required packages
- [ ] Implement timezone-aware date calculations (GMT+1)
- [ ] Build audio service with background playback
- [ ] Create caching system with 24-hour expiration
- [ ] Implement fallback strategies for offline use

### ✅ UI Development
- [ ] Create responsive layouts for phone/tablet
- [ ] Implement dark theme throughout
- [ ] Build reusable components (SongItem, MiniPlayer, etc.)
- [ ] Add multi-language support (Goun/French)
- [ ] Test accessibility features

### ✅ Testing & Deployment
- [ ] Test on physical iOS and Android devices
- [ ] Verify audio works with phone locked/backgrounded
- [ ] Test network failure scenarios
- [ ] Validate timezone calculations across date boundaries
- [ ] Submit to app stores

This PRD provides all necessary technical details, business logic, and resources for a Flutter developer to recreate the NiseAmen app with full feature parity to the React Native version.