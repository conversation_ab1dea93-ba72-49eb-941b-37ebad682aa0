{"indexes": [{"collectionGroup": "conversations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isArchived", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "conversations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "model", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}], "fieldOverrides": []}